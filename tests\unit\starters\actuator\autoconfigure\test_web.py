#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WebAutoConfiguration 单元测试

测试 Actuator Web 自动配置类的功能，包括：
- 条件化配置验证
- Bean 创建和注册
- Web 集成检查
- 路由注册器配置
"""

import unittest
from unittest.mock import MagicM<PERSON>, Mock, patch

from miniboot.starters.actuator.autoconfigure.web import WebAutoConfiguration
from miniboot.starters.actuator.properties import ActuatorProperties


class TestWebAutoConfiguration(unittest.TestCase):
    """WebAutoConfiguration 单元测试类"""

    def setUp(self):
        """设置测试环境"""
        self.config = WebAutoConfiguration()

        # 创建模拟的 ActuatorProperties
        self.actuator_properties = ActuatorProperties()
        self.actuator_properties.enabled = True
        self.actuator_properties.web.enabled = True
        self.actuator_properties.web.base_path = "/actuator"

    def test_web_integration_checker_creation(self):
        """测试 WebIntegrationChecker Bean 创建"""
        with patch('miniboot.starters.actuator.autoconfigure.web.logger') as mock_logger:
            checker = self.config.web_integration_checker()

            # 验证返回的是正确的类型
            self.assertIsNotNone(checker)
            self.assertEqual(checker.__class__.__name__, 'WebIntegrationChecker')

            # 验证日志记录
            mock_logger.debug.assert_called_with("Creating WebIntegrationChecker bean")
            mock_logger.info.assert_called()

    @patch('miniboot.starters.actuator.autoconfigure.web.WebAutoConfiguration._get_fastapi_app')
    @patch('miniboot.starters.actuator.autoconfigure.web.WebAutoConfiguration._get_actuator_context')
    def test_actuator_route_registrar_creation_success(self, mock_get_context, mock_get_app):
        """测试 ActuatorRouteRegistrar Bean 创建成功"""
        # 设置模拟对象
        mock_app = Mock()
        mock_context = Mock()
        mock_checker = Mock()
        mock_checker.should_enable_web_integration.return_value = True

        mock_get_app.return_value = mock_app
        mock_get_context.return_value = mock_context

        with patch('miniboot.starters.actuator.autoconfigure.web.logger') as mock_logger:
            registrar = self.config.actuator_route_registrar(
                self.actuator_properties,
                mock_checker
            )

            # 验证返回的是正确的类型
            self.assertIsNotNone(registrar)
            self.assertEqual(registrar.__class__.__name__, 'ActuatorRouteRegistrar')

            # 验证方法调用
            mock_checker.should_enable_web_integration.assert_called_once()
            mock_get_app.assert_called_once()
            mock_get_context.assert_called_once_with(self.actuator_properties)

            # 验证日志记录
            mock_logger.debug.assert_called_with("Creating ActuatorRouteRegistrar bean")
            mock_logger.info.assert_called()

    def test_actuator_route_registrar_creation_disabled(self):
        """测试 Web 集成禁用时不创建 ActuatorRouteRegistrar"""
        mock_checker = Mock()
        mock_checker.should_enable_web_integration.return_value = False

        with patch('miniboot.starters.actuator.autoconfigure.web.logger') as mock_logger:
            registrar = self.config.actuator_route_registrar(
                self.actuator_properties,
                mock_checker
            )

            # 验证返回 None
            self.assertIsNone(registrar)

            # 验证方法调用
            mock_checker.should_enable_web_integration.assert_called_once()

            # 验证日志记录
            mock_logger.info.assert_called_with(
                "Web integration conditions not met, skipping route registrar creation"
            )

    def test_get_fastapi_app_not_available(self):
        """测试 FastAPI 不可用时的处理"""
        with patch('miniboot.starters.actuator.autoconfigure.web.logger') as mock_logger:
            app = self.config._get_fastapi_app()

            # 验证返回 None（因为方法总是返回 None）
            self.assertIsNone(app)

            # 不验证特定的日志调用，因为实现可能不同

    @patch('miniboot.actuator.context.ActuatorContext')
    @patch('miniboot.actuator.properties.ActuatorProperties')
    def test_get_actuator_context_success(self, mock_core_props_class, mock_context_class):
        """测试成功获取 ActuatorContext"""
        # 设置模拟对象
        mock_core_props = Mock()
        mock_core_props_class.return_value = mock_core_props
        mock_context = Mock()
        mock_context_class.return_value = mock_context

        with patch('miniboot.starters.actuator.autoconfigure.web.logger') as mock_logger:
            context = self.config._get_actuator_context(self.actuator_properties)

            # 验证返回正确的上下文
            self.assertEqual(context, mock_context)

            # 验证 Core 属性创建
            mock_core_props_class.assert_called_once_with(
                enabled=self.actuator_properties.enabled,
                base_path=self.actuator_properties.web.base_path
            )

            # 验证 ActuatorContext 创建
            mock_context_class.assert_called_once_with(
                properties=mock_core_props,
                auto_load_config=False
            )

            # 验证日志记录
            mock_logger.debug.assert_called_with("Created ActuatorContext for route registrar")

    @patch('miniboot.actuator.context.ActuatorContext')
    def test_get_actuator_context_failure(self, mock_context_class):
        """测试获取 ActuatorContext 失败时的处理"""
        # 设置模拟异常
        mock_context_class.side_effect = Exception("Test error")

        with patch('miniboot.starters.actuator.autoconfigure.web.logger') as mock_logger:
            with self.assertRaises(Exception):
                self.config._get_actuator_context(self.actuator_properties)

            # 验证错误日志记录
            mock_logger.error.assert_called_with("Failed to get ActuatorContext: Test error")

    def test_actuator_web_middleware_not_implemented(self):
        """测试 Web 中间件（未实现）"""
        with patch('miniboot.starters.actuator.autoconfigure.web.logger') as mock_logger:
            middleware = self.config.actuator_web_middleware()

            # 验证返回 None
            self.assertIsNone(middleware)

            # 验证日志记录
            mock_logger.debug.assert_called_with("ActuatorWebMiddleware not implemented yet")

    def test_configuration_class_annotations(self):
        """测试配置类的注解"""
        # 验证类存在
        self.assertIsNotNone(WebAutoConfiguration)

        # 验证类是可实例化的
        config = WebAutoConfiguration()
        self.assertIsNotNone(config)

    def test_bean_method_annotations(self):
        """测试 Bean 方法的注解"""
        # 验证方法存在
        self.assertTrue(hasattr(self.config, 'web_integration_checker'))
        self.assertTrue(hasattr(self.config, 'actuator_route_registrar'))
        self.assertTrue(hasattr(self.config, 'actuator_web_middleware'))

        # 验证方法是可调用的
        self.assertTrue(callable(self.config.web_integration_checker))
        self.assertTrue(callable(self.config.actuator_route_registrar))
        self.assertTrue(callable(self.config.actuator_web_middleware))


if __name__ == '__main__':
    unittest.main()
