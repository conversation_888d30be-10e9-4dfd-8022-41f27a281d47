#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试指标收集器是否能被正确发现和注册
"""

import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath('.'))

from miniboot.context.application import DefaultApplicationContext
from miniboot.monitoring.discovery import MonitoringComponentDiscovery
from miniboot.monitoring.interfaces import MetricsCollector


async def test_metrics_collectors():
    """测试指标收集器发现和注册"""
    print("🚀 Testing Metrics Collectors Discovery...")

    try:
        # 1. 创建应用上下文
        print("📦 Creating application context...")
        context = DefaultApplicationContext(
            config_path="resources/application.yml",
            packages_to_scan=["miniboot.starters.actuator"],
            auto_detect=True
        )

        # 4. 启动上下文
        print("🔄 Starting application context...")
        await context.start()

        # 5. 检查 Bean 工厂状态
        print("🔍 Checking bean factory status...")
        bean_names = context.get_bean_names()
        print(f"📊 Total beans registered: {len(bean_names)}")

        # 6. 查找指标收集器 Bean
        metrics_collector_beans = []
        for bean_name in bean_names:
            try:
                bean = context.get_bean(bean_name)
                if isinstance(bean, MetricsCollector):
                    metrics_collector_beans.append((bean_name, bean))
                    print(f"✅ Found MetricsCollector: {bean_name} -> {type(bean).__name__}")
            except Exception as e:
                print(f"⚠️  Failed to get bean {bean_name}: {e}")

        print(f"📈 Total MetricsCollector beans found: {len(metrics_collector_beans)}")

        # 7. 使用组件发现系统
        print("🔍 Using ComponentDiscovery system...")
        discovery = MonitoringComponentDiscovery(context)

        # 发现指标收集器
        collectors = discovery.discover_metrics_collectors()
        print(f"📊 Discovered {len(collectors)} MetricsCollector implementations")

        for collector in collectors:
            print(f"  - {collector.get_collector_name()}: {type(collector).__name__}")
            print(f"    Available: {collector.is_available()}")
            print(f"    Supported metrics: {collector.get_supported_metrics()}")

            # 测试收集指标
            try:
                metrics_data = collector.collect_metrics()
                print(f"    Collected {len(metrics_data)} metrics")
                for metric in metrics_data[:3]:  # 只显示前3个
                    print(f"      - {metric.name}: {metric.value} {metric.unit or ''}")
            except Exception as e:
                print(f"    ❌ Failed to collect metrics: {e}")

        # 8. 发现其他组件
        contexts = discovery.discover_monitoring_contexts()
        endpoints = discovery.discover_endpoint_providers()

        print(f"🎯 Discovered {len(contexts)} MonitoringContext implementations")
        print(f"🌐 Discovered {len(endpoints)} EndpointProvider implementations")

        print("✅ Test completed successfully!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

    finally:
        try:
            if 'context' in locals():
                await context.stop()
                print("🛑 Application context stopped")
        except Exception as e:
            print(f"⚠️  Failed to stop context: {e}")

if __name__ == "__main__":
    asyncio.run(test_metrics_collectors())
