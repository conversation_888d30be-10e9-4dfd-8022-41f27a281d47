#!/usr/bin/env python3
"""
Actuator 端到端功能测试
验证 Actuator 模块的完整监控和数据采集功能
"""

import asyncio
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from miniboot.context.application import DefaultApplicationContext
from miniboot.env.environment import StandardEnvironment


async def test_actuator_health_endpoint():
    """测试健康检查端点功能"""
    print("🔍 Testing Actuator Health Endpoint...")

    try:
        # 创建环境
        environment = StandardEnvironment()
        environment.set_active_profiles('dev')

        # 创建应用上下文
        context = DefaultApplicationContext()
        context._environment = environment

        # 执行自动配置
        await context._execute_auto_configurations()

        # 获取健康端点
        health_endpoint = context.get_bean_factory().get('health_endpoint_provider')
        if not health_endpoint:
            print("  ❌ Health endpoint not found")
            return False

        print(f"  📦 Health endpoint type: {type(health_endpoint).__name__}")

        # 测试健康检查
        # 创建一个模拟请求对象
        mock_request = {"method": "GET", "path": "/actuator/health"}
        health_data = await health_endpoint.handle_request(mock_request)
        print(f"  📊 Health data: {health_data}")

        # 验证健康数据格式
        if isinstance(health_data, dict) and 'status' in health_data:
            print(f"  ✅ Health check successful: {health_data['status']}")
            return True
        else:
            print(f"  ❌ Invalid health data format: {health_data}")
            return False

    except Exception as e:
        print(f"  ❌ Error testing health endpoint: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_actuator_context_functionality():
    """测试 ActuatorContext 功能"""
    print("\n🔍 Testing ActuatorContext Functionality...")

    try:
        # 创建环境
        environment = StandardEnvironment()
        environment.set_active_profiles('dev')

        # 创建应用上下文
        context = DefaultApplicationContext()
        context._environment = environment

        # 执行自动配置
        await context._execute_auto_configurations()

        # 获取 ActuatorContext
        actuator_context = context.get_bean_factory().get('monitoring_context')
        if not actuator_context:
            print("  ❌ ActuatorContext not found")
            return False

        print(f"  📦 ActuatorContext type: {type(actuator_context).__name__}")
        print(f"  📊 Is started: {actuator_context.is_started}")
        print(f"  📊 Properties enabled: {actuator_context.properties.enabled}")

        # 测试端点注册
        endpoints = actuator_context.get_endpoints()
        print(f"  📊 Registered endpoints: {len(endpoints)}")

        for endpoint_id, endpoint in endpoints.items():
            print(f"    📌 {endpoint_id}: {type(endpoint).__name__}")

        return len(endpoints) > 0

    except Exception as e:
        print(f"  ❌ Error testing ActuatorContext: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_metrics_collection():
    """测试指标收集功能"""
    print("\n🔍 Testing Metrics Collection...")

    try:
        # 创建环境
        environment = StandardEnvironment()
        environment.set_active_profiles('dev')

        # 创建应用上下文
        context = DefaultApplicationContext()
        context._environment = environment

        # 执行自动配置
        await context._execute_auto_configurations()

        # 获取指标收集器
        bean_names = context.get_bean_factory().names()
        metrics_collectors = [name for name in bean_names if 'metrics_collector' in name]

        print(f"  📊 Found {len(metrics_collectors)} metrics collectors:")
        for collector_name in metrics_collectors:
            collector = context.get_bean_factory().get(collector_name)
            print(f"    📌 {collector_name}: {type(collector).__name__}")

            # 尝试收集指标
            if hasattr(collector, 'collect_metrics'):
                try:
                    metrics = await collector.collect_metrics()
                    print(f"      📈 Collected metrics: {len(metrics) if isinstance(metrics, (list, dict)) else 'N/A'}")
                except Exception as e:
                    print(f"      ⚠️ Metrics collection failed: {e}")

        return len(metrics_collectors) > 0

    except Exception as e:
        print(f"  ❌ Error testing metrics collection: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance_monitoring():
    """测试性能监控功能"""
    print("\n🔍 Testing Performance Monitoring...")

    try:
        # 创建环境
        environment = StandardEnvironment()
        environment.set_active_profiles('dev')

        # 创建应用上下文
        context = DefaultApplicationContext()
        context._environment = environment

        # 执行自动配置
        await context._execute_auto_configurations()

        # 获取性能指标注册表
        metrics_registry = context.get_bean_factory().get('metrics_registry')
        if not metrics_registry:
            print("  ❌ Metrics registry not found")
            return False

        print(f"  📦 Metrics registry type: {type(metrics_registry).__name__}")

        # 测试性能监控
        start_time = time.time()

        # 模拟一些操作
        await asyncio.sleep(0.1)

        end_time = time.time()
        duration = end_time - start_time

        print(f"  📊 Test operation duration: {duration:.3f}s")

        # 检查是否有性能指标
        if hasattr(metrics_registry, 'get_metrics'):
            try:
                metrics = metrics_registry.get_metrics()
                print(f"  📈 Registry metrics: {len(metrics) if isinstance(metrics, (list, dict)) else 'Available'}")
            except Exception as e:
                print(f"  ⚠️ Failed to get registry metrics: {e}")

        return True

    except Exception as e:
        print(f"  ❌ Error testing performance monitoring: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🎯 Actuator End-to-End Functionality Test")
    print("=" * 60)

    # 测试 1: 健康检查端点
    health_success = await test_actuator_health_endpoint()

    # 测试 2: ActuatorContext 功能
    context_success = await test_actuator_context_functionality()

    # 测试 3: 指标收集
    metrics_success = await test_metrics_collection()

    # 测试 4: 性能监控
    performance_success = await test_performance_monitoring()

    # 总结
    print("\n📊 Test Results Summary:")
    print("=" * 60)
    print(f"{'✅' if health_success else '❌'} Health Endpoint: {'SUCCESS' if health_success else 'FAILED'}")
    print(f"{'✅' if context_success else '❌'} ActuatorContext: {'SUCCESS' if context_success else 'FAILED'}")
    print(f"{'✅' if metrics_success else '❌'} Metrics Collection: {'SUCCESS' if metrics_success else 'FAILED'}")
    print(f"{'✅' if performance_success else '❌'} Performance Monitoring: {'SUCCESS' if performance_success else 'FAILED'}")

    # 判断整体结果
    overall_success = health_success and context_success and metrics_success and performance_success

    print(f"\n🎯 Overall Result: {'✅ SUCCESS' if overall_success else '❌ FAILED'}")

    if overall_success:
        print("\n🎉 Actuator module is fully functional!")
        print("  - Health endpoints are working")
        print("  - Monitoring context is active")
        print("  - Metrics collection is operational")
        print("  - Performance monitoring is enabled")
    else:
        print("\n🔧 Some Actuator features need attention:")
        if not health_success:
            print("  - Health endpoint functionality issues")
        if not context_success:
            print("  - ActuatorContext integration problems")
        if not metrics_success:
            print("  - Metrics collection not working properly")
        if not performance_success:
            print("  - Performance monitoring issues")

if __name__ == "__main__":
    asyncio.run(main())
