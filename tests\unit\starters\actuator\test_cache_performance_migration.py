#!/usr/bin/env python
# encoding: utf-8
"""
测试缓存和性能模块迁移 - 任务2.4验证

验证缓存和性能模块从 miniboot/actuator/{cache,performance}/ 迁移到
miniboot/starters/actuator/{cache,performance}/ 的正确性
"""

import unittest
from unittest.mock import Mock, patch


class TestCachePerformanceMigration(unittest.TestCase):
    """缓存和性能模块迁移测试"""

    def test_cache_module_imports(self):
        """测试缓存模块导入"""
        # 测试从缓存模块直接导入
        from miniboot.starters.actuator.cache import (AdvancedEndpointCache,
                                                      CacheEndpoint,
                                                      CacheManager,
                                                      CacheStrategy,
                                                      adaptive_cache,
                                                      lru_cache)

        # 验证类可用
        self.assertTrue(callable(CacheManager))
        self.assertTrue(callable(CacheEndpoint))
        self.assertTrue(callable(AdvancedEndpointCache))

        # 验证枚举
        self.assertEqual(CacheStrategy.LRU.value, "LRU")
        self.assertEqual(CacheStrategy.LFU.value, "LFU")

    def test_performance_module_imports(self):
        """测试性能模块导入"""
        # 测试从性能模块直接导入
        from miniboot.starters.actuator.performance import (
            MetricType, PerformanceMetrics, PerformanceMonitor,
            get_metrics_registry)

        # 验证类可用
        self.assertTrue(callable(PerformanceMonitor))
        self.assertTrue(callable(PerformanceMetrics))

        # 验证枚举
        self.assertEqual(MetricType.RESPONSE_TIME.value, "response_time")
        self.assertEqual(MetricType.THROUGHPUT.value, "throughput")

    def test_main_module_exports(self):
        """测试主模块导出缓存和性能组件"""
        from miniboot.starters.actuator import (AdvancedEndpointCache,
                                                CacheEndpoint, CacheManager,
                                                PerformanceMetrics,
                                                PerformanceMonitor)

        # 验证从主模块可以导入缓存和性能组件
        self.assertTrue(callable(CacheManager))
        self.assertTrue(callable(PerformanceMonitor))
        self.assertTrue(callable(CacheEndpoint))

    def test_cache_manager_instantiation(self):
        """测试CacheManager实例化"""
        from miniboot.starters.actuator.cache import CacheManager

        cache_manager = CacheManager()
        self.assertIsNotNone(cache_manager)
        self.assertIsNotNone(cache_manager.caches)  # 缓存字典

    def test_performance_monitor_instantiation(self):
        """测试PerformanceMonitor实例化"""
        from miniboot.starters.actuator.performance import PerformanceMonitor

        performance_monitor = PerformanceMonitor()
        self.assertIsNotNone(performance_monitor)
        self.assertIsNotNone(performance_monitor.metrics_registry)

    def test_cache_endpoint_instantiation(self):
        """测试CacheEndpoint实例化"""
        from miniboot.starters.actuator.cache import CacheEndpoint

        endpoint = CacheEndpoint()
        self.assertIsNotNone(endpoint)
        self.assertEqual(endpoint.id, "cache")
        self.assertTrue(endpoint.enabled)

    def test_performance_endpoint_instantiation(self):
        """测试PerformanceEndpoint实例化"""
        from miniboot.starters.actuator.performance import PerformanceEndpoint

        endpoint = PerformanceEndpoint()
        self.assertIsNotNone(endpoint)
        self.assertEqual(endpoint.id, "performance")
        self.assertTrue(endpoint.enabled)

    def test_cache_strategies(self):
        """测试缓存策略"""
        from miniboot.starters.actuator.cache import CacheStrategy

        # 测试所有缓存策略
        self.assertEqual(CacheStrategy.LRU.value, "LRU")
        self.assertEqual(CacheStrategy.LFU.value, "LFU")
        self.assertEqual(CacheStrategy.FIFO.value, "FIFO")
        self.assertEqual(CacheStrategy.TTL.value, "TTL")

    def test_performance_metric_types(self):
        """测试性能指标类型"""
        from miniboot.starters.actuator.performance import MetricType

        # 测试所有指标类型
        self.assertEqual(MetricType.RESPONSE_TIME.value, "response_time")
        self.assertEqual(MetricType.THROUGHPUT.value, "throughput")
        self.assertEqual(MetricType.CONCURRENCY.value, "concurrency")
        self.assertEqual(MetricType.ERROR_RATE.value, "error_rate")

    def test_cache_decorators(self):
        """测试缓存装饰器"""
        from miniboot.starters.actuator.cache import (adaptive_cache,
                                                      lru_cache, ttl_cache)

        # 验证装饰器可用
        self.assertTrue(callable(lru_cache))
        self.assertTrue(callable(ttl_cache))
        self.assertTrue(callable(adaptive_cache))

    def test_performance_decorators(self):
        """测试性能装饰器"""
        from miniboot.starters.actuator.performance import (counted, monitor,
                                                            timed)

        # 验证装饰器可用
        self.assertTrue(callable(timed))
        self.assertTrue(callable(monitor))
        self.assertTrue(callable(counted))

    def test_monitoring_performance_dependency_restored(self):
        """测试监控模块的性能依赖已恢复"""
        from miniboot.starters.actuator.monitoring import MonitoringAlerts

        alerts = MonitoringAlerts()
        # 检查性能指标注册表是否正确初始化
        self.assertIsNotNone(alerts.metrics_registry)

        # 验证可以获取性能指标
        from miniboot.starters.actuator.performance import get_metrics_registry
        registry = get_metrics_registry()
        self.assertIsNotNone(registry)

    def test_cache_module_structure(self):
        """测试缓存模块结构"""
        import miniboot.starters.actuator.cache as cache_module

        # 验证模块包含预期的组件
        expected_components = [
            'CacheManager', 'CacheEndpoint', 'AdvancedEndpointCache',
            'CacheStrategy', 'lru_cache', 'ttl_cache', 'adaptive_cache'
        ]

        for component in expected_components:
            self.assertTrue(hasattr(cache_module, component),
                          f"缓存模块缺少组件: {component}")

    def test_performance_module_structure(self):
        """测试性能模块结构"""
        import miniboot.starters.actuator.performance as performance_module

        # 验证模块包含预期的组件
        expected_components = [
            'PerformanceMonitor', 'PerformanceMetrics',
            'MetricType', 'get_metrics_registry', 'timed', 'monitor'
        ]

        for component in expected_components:
            self.assertTrue(hasattr(performance_module, component),
                          f"性能模块缺少组件: {component}")

    def test_import_paths_updated(self):
        """测试导入路径已正确更新"""
        # 验证可以从新路径导入
        try:
            from miniboot.starters.actuator.cache.cache import CacheManager
            from miniboot.starters.actuator.cache.endpoint import CacheEndpoint
            from miniboot.starters.actuator.performance.monitor import \
                PerformanceMonitor

            # 如果能成功导入，说明路径正确
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"导入路径更新失败: {e}")

    def test_cross_module_integration(self):
        """测试模块间集成"""
        from miniboot.starters.actuator.cache import CacheManager
        from miniboot.starters.actuator.monitoring import MonitoringAlerts
        from miniboot.starters.actuator.performance import PerformanceMonitor

        # 验证各模块可以协同工作
        cache_manager = CacheManager()
        performance_monitor = PerformanceMonitor()
        alerts = MonitoringAlerts()

        # 验证监控系统可以访问性能指标
        self.assertIsNotNone(alerts.metrics_registry)
        self.assertEqual(alerts.metrics_registry, performance_monitor.metrics_registry)

    def test_endpoint_operations(self):
        """测试端点操作"""
        from miniboot.starters.actuator.cache import CacheEndpoint
        from miniboot.starters.actuator.performance import PerformanceEndpoint

        cache_endpoint = CacheEndpoint()
        performance_endpoint = PerformanceEndpoint()

        # 测试端点操作
        cache_operations = cache_endpoint.operations
        performance_operations = performance_endpoint.operations

        self.assertIsInstance(cache_operations, list)
        self.assertIsInstance(performance_operations, list)
        self.assertGreater(len(cache_operations), 0)
        self.assertGreater(len(performance_operations), 0)


if __name__ == '__main__':
    unittest.main()
