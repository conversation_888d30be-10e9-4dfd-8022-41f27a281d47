#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试 Actuator 动态路由注册器

测试 ActuatorRouteRegistrar 类的各项功能，包括：
- 路由注册和注销
- 基础路径配置
- 端点路径映射
- 路由状态管理
- 错误处理和异常情况
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import time

from miniboot.starters.actuator.web.routes import (
    ActuatorRouteRegistrar, 
    RouteInfo, 
    RouteRegistrationStats
)


class MockEndpointOperation:
    """模拟端点操作"""
    
    def __init__(self, operation_type, method, path=""):
        self.operation_type = Mock()
        self.operation_type.value = operation_type
        self.method = method
        self.path = path


class MockEndpoint:
    """模拟端点"""
    
    def __init__(self, endpoint_id, enabled=True, sensitive=False, operations=None):
        self.id = endpoint_id
        self.enabled = enabled
        self.sensitive = sensitive
        self._operations = operations or []
    
    def operations(self):
        return self._operations
    
    def invoke(self, operation_type):
        return {"endpoint": self.id, "operation": operation_type, "data": "test"}


class MockActuatorContext:
    """模拟 Actuator 上下文"""
    
    def __init__(self, endpoints=None):
        self._endpoints = endpoints or {}
    
    def get_all_endpoints(self):
        return self._endpoints


class MockFastAPIApp:
    """模拟 FastAPI 应用"""
    
    def __init__(self):
        self.routes = []
        self.registered_routes = {}
    
    def get(self, path):
        def decorator(handler):
            self.registered_routes[f"GET:{path}"] = handler
            return handler
        return decorator
    
    def post(self, path):
        def decorator(handler):
            self.registered_routes[f"POST:{path}"] = handler
            return handler
        return decorator
    
    def put(self, path):
        def decorator(handler):
            self.registered_routes[f"PUT:{path}"] = handler
            return handler
        return decorator
    
    def delete(self, path):
        def decorator(handler):
            self.registered_routes[f"DELETE:{path}"] = handler
            return handler
        return decorator


class TestRouteInfo(unittest.TestCase):
    """测试 RouteInfo 数据类"""
    
    def test_route_info_creation(self):
        """测试路由信息创建"""
        handler = lambda: "test"
        route_info = RouteInfo(
            endpoint_id="health",
            path="/health",
            method="GET",
            handler=handler,
            full_path="/actuator/health"
        )
        
        self.assertEqual(route_info.endpoint_id, "health")
        self.assertEqual(route_info.path, "/health")
        self.assertEqual(route_info.method, "GET")
        self.assertEqual(route_info.handler, handler)
        self.assertEqual(route_info.full_path, "/actuator/health")
        self.assertEqual(route_info.operation_type, "READ")
        self.assertFalse(route_info.sensitive)
        self.assertGreater(route_info.registered_at, 0)
    
    def test_route_info_with_custom_values(self):
        """测试自定义值的路由信息"""
        handler = lambda: "test"
        route_info = RouteInfo(
            endpoint_id="metrics",
            path="/metrics",
            method="POST",
            handler=handler,
            full_path="/actuator/metrics",
            operation_type="WRITE",
            sensitive=True
        )
        
        self.assertEqual(route_info.operation_type, "WRITE")
        self.assertTrue(route_info.sensitive)


class TestRouteRegistrationStats(unittest.TestCase):
    """测试路由注册统计信息"""
    
    def test_stats_initialization(self):
        """测试统计信息初始化"""
        stats = RouteRegistrationStats()
        
        self.assertEqual(stats.total_routes, 0)
        self.assertEqual(stats.registered_routes, 0)
        self.assertEqual(stats.failed_routes, 0)
        self.assertEqual(stats.last_registration_time, 0)
        self.assertEqual(len(stats.registration_errors), 0)
    
    def test_stats_reset(self):
        """测试统计信息重置"""
        stats = RouteRegistrationStats()
        stats.total_routes = 5
        stats.registered_routes = 3
        stats.failed_routes = 2
        stats.last_registration_time = 1.5
        stats.registration_errors.append("error")
        
        stats.reset()
        
        self.assertEqual(stats.total_routes, 0)
        self.assertEqual(stats.registered_routes, 0)
        self.assertEqual(stats.failed_routes, 0)
        self.assertEqual(stats.last_registration_time, 0)
        self.assertEqual(len(stats.registration_errors), 0)


class TestActuatorRouteRegistrar(unittest.TestCase):
    """测试 ActuatorRouteRegistrar 类"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_app = MockFastAPIApp()
        self.mock_context = MockActuatorContext()
        self.registrar = ActuatorRouteRegistrar(
            app=self.mock_app,
            actuator_context=self.mock_context,
            base_path="/actuator"
        )
    
    def test_initialization(self):
        """测试初始化"""
        registrar = ActuatorRouteRegistrar()
        
        self.assertIsNone(registrar._app)
        self.assertIsNone(registrar._actuator_context)
        self.assertEqual(registrar._base_path, "/actuator")
        self.assertEqual(len(registrar._registered_routes), 0)
        self.assertEqual(len(registrar._route_handlers), 0)
        self.assertEqual(len(registrar._endpoint_route_mapping), 0)
    
    def test_initialization_with_custom_base_path(self):
        """测试自定义基础路径初始化"""
        registrar = ActuatorRouteRegistrar(base_path="/management/")
        
        self.assertEqual(registrar._base_path, "/management")
    
    def test_set_app(self):
        """测试设置 FastAPI 应用"""
        registrar = ActuatorRouteRegistrar()
        app = MockFastAPIApp()
        
        registrar.set_app(app)
        
        self.assertEqual(registrar._app, app)
    
    def test_set_actuator_context(self):
        """测试设置 Actuator 上下文"""
        registrar = ActuatorRouteRegistrar()
        context = MockActuatorContext()
        
        registrar.set_actuator_context(context)
        
        self.assertEqual(registrar._actuator_context, context)
    
    def test_set_base_path(self):
        """测试设置基础路径"""
        registrar = ActuatorRouteRegistrar()
        
        registrar.set_base_path("/management/")
        
        self.assertEqual(registrar._base_path, "/management")
    
    @patch('miniboot.starters.actuator.web.routes.FASTAPI_AVAILABLE', True)
    def test_register_routes_success(self):
        """测试成功注册路由"""
        # 创建模拟端点
        health_endpoint = MockEndpoint(
            "health", 
            enabled=True,
            operations=[MockEndpointOperation("READ", "GET")]
        )
        
        metrics_endpoint = MockEndpoint(
            "metrics",
            enabled=True,
            operations=[MockEndpointOperation("READ", "GET")]
        )
        
        # 设置模拟上下文
        self.mock_context._endpoints = {
            "health": health_endpoint,
            "metrics": metrics_endpoint
        }
        
        # 注册路由
        result = self.registrar.register_routes()
        
        # 验证结果
        self.assertTrue(result)
        self.assertEqual(len(self.registrar._registered_routes), 2)
        self.assertEqual(len(self.registrar._endpoint_route_mapping), 2)
        self.assertIn("health", self.registrar._endpoint_route_mapping)
        self.assertIn("metrics", self.registrar._endpoint_route_mapping)
    
    @patch('miniboot.starters.actuator.web.routes.FASTAPI_AVAILABLE', True)
    def test_register_endpoint_route(self):
        """测试注册单个端点路由"""
        # 创建模拟端点
        endpoint = MockEndpoint(
            "health",
            enabled=True,
            operations=[
                MockEndpointOperation("READ", "GET"),
                MockEndpointOperation("WRITE", "POST", "/reset")
            ]
        )
        
        # 注册端点路由
        result = self.registrar.register_endpoint_route("health", endpoint)
        
        # 验证结果
        self.assertTrue(result)
        self.assertEqual(len(self.registrar._registered_routes), 2)
        self.assertIn("health", self.registrar._endpoint_route_mapping)
        self.assertEqual(len(self.registrar._endpoint_route_mapping["health"]), 2)
    
    def test_unregister_endpoint_routes(self):
        """测试注销端点路由"""
        # 先注册一些路由
        self.registrar._registered_routes["health:GET:/health"] = RouteInfo(
            endpoint_id="health",
            path="/health",
            method="GET",
            handler=lambda: None,
            full_path="/actuator/health"
        )
        self.registrar._route_handlers["health:GET:/health"] = lambda: None
        self.registrar._endpoint_route_mapping["health"] = ["health:GET:/health"]
        
        # 注销路由
        result = self.registrar.unregister_endpoint_routes("health")
        
        # 验证结果
        self.assertTrue(result)
        self.assertEqual(len(self.registrar._registered_routes), 0)
        self.assertEqual(len(self.registrar._route_handlers), 0)
        self.assertNotIn("health", self.registrar._endpoint_route_mapping)
    
    def test_get_registered_routes(self):
        """测试获取已注册路由"""
        # 添加一些路由
        route_info = RouteInfo(
            endpoint_id="health",
            path="/health",
            method="GET",
            handler=lambda: None,
            full_path="/actuator/health"
        )
        self.registrar._registered_routes["health:GET:/health"] = route_info
        
        # 获取路由
        routes = self.registrar.get_registered_routes()
        
        # 验证结果
        self.assertEqual(len(routes), 1)
        self.assertIn("health:GET:/health", routes)
        self.assertEqual(routes["health:GET:/health"], route_info)
    
    def test_get_endpoint_routes(self):
        """测试获取指定端点的路由"""
        # 添加路由
        route_info = RouteInfo(
            endpoint_id="health",
            path="/health",
            method="GET",
            handler=lambda: None,
            full_path="/actuator/health"
        )
        self.registrar._registered_routes["health:GET:/health"] = route_info
        self.registrar._endpoint_route_mapping["health"] = ["health:GET:/health"]
        
        # 获取端点路由
        routes = self.registrar.get_endpoint_routes("health")
        
        # 验证结果
        self.assertEqual(len(routes), 1)
        self.assertEqual(routes[0], route_info)
    
    def test_get_route_status(self):
        """测试获取路由状态"""
        # 添加一些数据
        self.registrar._endpoint_route_mapping["health"] = ["health:GET:/health"]
        self.registrar._registered_routes["health:GET:/health"] = RouteInfo(
            endpoint_id="health",
            path="/health",
            method="GET",
            handler=lambda: None,
            full_path="/actuator/health"
        )
        
        # 获取状态
        status = self.registrar.get_route_status()
        
        # 验证结果
        self.assertEqual(status['base_path'], "/actuator")
        self.assertEqual(status['total_endpoints'], 1)
        self.assertEqual(status['total_routes'], 1)
        self.assertIn('registration_stats', status)
        self.assertIn('endpoints', status)
        self.assertIn('fastapi_available', status)
        self.assertIn('app_configured', status)
        self.assertIn('context_configured', status)
        self.assertIn('timestamp', status)
    
    def test_build_route_path(self):
        """测试构建路由路径"""
        # 测试无子路径
        path = self.registrar._build_route_path("health")
        self.assertEqual(path, "/health")
        
        # 测试有子路径（相对路径）
        path = self.registrar._build_route_path("metrics", "memory")
        self.assertEqual(path, "/metrics/memory")
        
        # 测试有子路径（绝对路径）
        path = self.registrar._build_route_path("custom", "/status")
        self.assertEqual(path, "/custom/status")
    
    @patch('miniboot.starters.actuator.web.routes.FASTAPI_AVAILABLE', False)
    def test_validate_dependencies_no_fastapi(self):
        """测试 FastAPI 不可用时的依赖验证"""
        result = self.registrar._validate_dependencies()
        self.assertFalse(result)
    
    def test_validate_dependencies_no_app(self):
        """测试无 FastAPI 应用时的依赖验证"""
        registrar = ActuatorRouteRegistrar(actuator_context=self.mock_context)
        result = registrar._validate_dependencies()
        self.assertFalse(result)
    
    def test_validate_dependencies_no_context(self):
        """测试无 Actuator 上下文时的依赖验证"""
        registrar = ActuatorRouteRegistrar(app=self.mock_app)
        result = registrar._validate_dependencies()
        self.assertFalse(result)
    
    def test_get_enabled_endpoints(self):
        """测试获取启用的端点"""
        # 创建端点
        enabled_endpoint = MockEndpoint("health", enabled=True)
        disabled_endpoint = MockEndpoint("debug", enabled=False)
        
        self.mock_context._endpoints = {
            "health": enabled_endpoint,
            "debug": disabled_endpoint
        }
        
        # 获取启用的端点
        enabled = self.registrar._get_enabled_endpoints()
        
        # 验证结果
        self.assertEqual(len(enabled), 1)
        self.assertIn("health", enabled)
        self.assertNotIn("debug", enabled)


if __name__ == '__main__':
    unittest.main()
