#!/usr/bin/env python
# encoding: utf-8
"""
任务2.5：安全模块迁移验证测试

验证安全模块从 miniboot/actuator/security/ 迁移到 miniboot/starters/actuator/security/ 的完整性
"""

import os
import unittest
from pathlib import Path


class TestMigrationTask25(unittest.TestCase):
    """任务2.5：安全模块迁移验证"""

    def test_security_module_migration_success(self):
        """验证安全模块文件迁移成功"""
        print("\n🔍 验证安全模块文件迁移...")

        # 检查目标目录存在
        target_dir = Path("miniboot/starters/actuator/security")
        self.assertTrue(target_dir.exists(), f"目标目录不存在: {target_dir}")

        # 检查必要文件存在
        required_files = [
            "__init__.py",
            "integration.py",
            "manager.py",
            "middleware.py",
            "security.py"
        ]

        for file_name in required_files:
            file_path = target_dir / file_name
            self.assertTrue(file_path.exists(), f"文件不存在: {file_path}")
            print(f"✅ {file_name} 迁移成功")

    def test_security_components_available(self):
        """验证安全组件可用性"""
        print("\n🔍 验证安全组件可用性...")

        try:
            # 测试核心安全组件导入
            from miniboot.starters.actuator.security import (
                ActuatorSecurity, ProductionSecurityManager,
                SecurityIntegration, SecurityLevel, SecurityMiddleware,
                SecurityPolicy, SecurityToken)

            # 验证类
            self.assertTrue(callable(ActuatorSecurity))
            self.assertTrue(callable(ProductionSecurityManager))
            self.assertTrue(callable(SecurityIntegration))
            self.assertTrue(callable(SecurityMiddleware))

            # 验证枚举
            self.assertIsNotNone(SecurityLevel.PRODUCTION)
            self.assertIsNotNone(SecurityLevel.DEVELOPMENT)

            print("✅ 安全组件可用性验证成功")
        except Exception as e:
            self.fail(f"❌ 安全组件验证失败: {e}")

    def test_main_module_exports_security(self):
        """验证主模块正确导出安全组件"""
        print("\n🔍 验证主模块安全组件导出...")

        try:
            # 从主模块导入安全组件
            from miniboot.starters.actuator import (ActuatorSecurity,
                                                    ProductionSecurityManager,
                                                    SecurityIntegration,
                                                    SecurityLevel,
                                                    SecurityMiddleware)

            # 验证导入成功
            self.assertIsNotNone(ActuatorSecurity)
            self.assertIsNotNone(SecurityIntegration)
            self.assertIsNotNone(ProductionSecurityManager)
            self.assertIsNotNone(SecurityLevel)
            self.assertIsNotNone(SecurityMiddleware)

            print("✅ 主模块安全组件导出验证成功")
        except Exception as e:
            self.fail(f"❌ 主模块导出验证失败: {e}")

    def test_security_basic_functionality(self):
        """验证安全组件基本功能"""
        print("\n🔍 验证安全组件基本功能...")

        try:
            from miniboot.starters.actuator.properties import \
                SecurityProperties
            from miniboot.starters.actuator.security import (ActuatorSecurity,
                                                             SecurityLevel)

            # 创建安全配置
            security_props = SecurityProperties()
            security_props.enabled = True
            security_props.authentication_required = False

            # 创建安全管理器
            security = ActuatorSecurity(security_props)

            # 验证基本属性
            self.assertEqual(security.enabled, True)
            self.assertIsNotNone(security.config)

            # 验证安全级别
            level = SecurityLevel.PRODUCTION
            self.assertEqual(level.value, "production")

            print("✅ 安全组件基本功能验证成功")
        except Exception as e:
            self.fail(f"❌ 安全组件功能验证失败: {e}")

    def test_import_paths_work(self):
        """验证导入路径正确"""
        print("\n🔍 验证导入路径...")

        try:
            # 测试各种导入方式
            import miniboot.starters.actuator.security
            from miniboot.starters.actuator.security import ActuatorSecurity
            from miniboot.starters.actuator.security.integration import \
                SecurityIntegration
            from miniboot.starters.actuator.security.manager import \
                ProductionSecurityManager
            from miniboot.starters.actuator.security.middleware import \
                SecurityMiddleware

            # 验证模块存在
            self.assertIsNotNone(miniboot.starters.actuator.security)
            self.assertIsNotNone(ActuatorSecurity)
            self.assertIsNotNone(ProductionSecurityManager)
            self.assertIsNotNone(SecurityMiddleware)
            self.assertIsNotNone(SecurityIntegration)

            print("✅ 导入路径验证成功")
        except Exception as e:
            self.fail(f"❌ 导入路径验证失败: {e}")

    def test_file_structure_migration(self):
        """验证文件结构迁移完整性"""
        print("\n🔍 验证文件结构迁移...")

        # 源目录应该已被删除（迁移完成）
        source_dir = Path("miniboot/actuator/security")
        self.assertFalse(source_dir.exists(), "源目录应该已被删除")

        # 目标目录应该存在
        target_dir = Path("miniboot/starters/actuator/security")
        self.assertTrue(target_dir.exists(), "目标目录应该存在")

        # 检查目标目录中的文件
        target_files = list(target_dir.glob("*.py"))
        expected_files = ["__init__.py", "integration.py", "manager.py", "middleware.py", "security.py"]

        self.assertEqual(len(target_files), len(expected_files),
                        f"目标目录文件数量不正确: 期望{len(expected_files)} vs 实际{len(target_files)}")

        print(f"✅ 文件结构验证成功: {len(target_files)} 个文件已迁移")

    def test_task_2_5_completion_summary(self):
        """任务2.5完成总结"""
        print("\n📋 任务2.5：安全模块迁移 - 完成总结")
        print("=" * 60)

        try:
            # 统计迁移的文件
            target_dir = Path("miniboot/starters/actuator/security")
            migrated_files = list(target_dir.glob("*.py"))

            print(f"✅ 迁移文件数量: {len(migrated_files)}")
            print(f"✅ 源路径: miniboot/actuator/security/")
            print(f"✅ 目标路径: miniboot/starters/actuator/security/")

            # 验证主要组件
            from miniboot.starters.actuator import (ActuatorSecurity,
                                                    SecurityIntegration)
            print("✅ 主模块导入: 成功")
            print("✅ 安全组件: 可用")

            print("=" * 60)
            print("🎉 任务2.5：安全模块迁移 - 完成！")

        except Exception as e:
            self.fail(f"❌ 任务2.5总结失败: {e}")


if __name__ == '__main__':
    unittest.main()
