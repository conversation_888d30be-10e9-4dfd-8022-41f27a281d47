#!/usr/bin/env python
# encoding: utf-8
"""
测试监控模块迁移 - 任务2.3验证

验证监控模块从 miniboot/actuator/monitoring/ 迁移到 miniboot/starters/actuator/monitoring/ 的正确性
"""

import unittest
from unittest.mock import Mock, patch


class TestMonitoringMigration(unittest.TestCase):
    """监控模块迁移测试"""

    def test_monitoring_module_imports(self):
        """测试监控模块导入"""
        # 测试从监控模块直接导入
        from miniboot.starters.actuator.monitoring import (AlertRule,
                                                           AlertSeverity,
                                                           AlertStatus,
                                                           HealthMonitor,
                                                           MonitoringAlerts)

        # 验证类可用
        self.assertTrue(callable(MonitoringAlerts))
        self.assertTrue(callable(AlertRule))
        self.assertTrue(callable(HealthMonitor))

        # 验证枚举
        self.assertEqual(AlertSeverity.CRITICAL.value, "critical")
        self.assertEqual(AlertStatus.ACTIVE.value, "active")

    def test_main_module_exports(self):
        """测试主模块导出监控组件"""
        from miniboot.starters.actuator import (AlertRule, AlertSeverity,
                                                HealthMonitor,
                                                MonitoringAlerts,
                                                MonitoringAlertsEndpoint)

        # 验证从主模块可以导入监控组件
        self.assertTrue(callable(MonitoringAlerts))
        self.assertTrue(callable(HealthMonitor))

    def test_monitoring_alerts_instantiation(self):
        """测试MonitoringAlerts实例化"""
        from miniboot.starters.actuator.monitoring import MonitoringAlerts

        alerts = MonitoringAlerts()
        self.assertIsNotNone(alerts)
        self.assertFalse(alerts._running)  # 初始状态应该是未运行

    def test_health_monitor_instantiation(self):
        """测试HealthMonitor实例化"""
        from miniboot.starters.actuator.monitoring import HealthMonitor

        health_monitor = HealthMonitor()
        self.assertIsNotNone(health_monitor)
        self.assertIsNotNone(health_monitor.metrics_aggregator)



    def test_alert_rule_creation(self):
        """测试AlertRule创建"""
        from miniboot.starters.actuator.monitoring import (AlertChannel,
                                                           AlertRule,
                                                           AlertSeverity)

        rule = AlertRule(
            rule_id="test_rule",
            name="Test Rule",
            description="Test alert rule",
            metric_name="test.metric",
            condition="value > 100",
            threshold=100,
            severity=AlertSeverity.WARNING,
            channels=[AlertChannel.LOG]
        )

        self.assertEqual(rule.rule_id, "test_rule")
        self.assertEqual(rule.name, "Test Rule")
        self.assertEqual(rule.severity, AlertSeverity.WARNING)

    def test_alert_enums(self):
        """测试告警相关枚举"""
        from miniboot.starters.actuator.monitoring import (AlertChannel,
                                                           AlertSeverity,
                                                           AlertStatus)

        # 测试AlertSeverity
        self.assertEqual(AlertSeverity.INFO.value, "info")
        self.assertEqual(AlertSeverity.WARNING.value, "warning")
        self.assertEqual(AlertSeverity.ERROR.value, "error")
        self.assertEqual(AlertSeverity.CRITICAL.value, "critical")

        # 测试AlertStatus
        self.assertEqual(AlertStatus.ACTIVE.value, "active")
        self.assertEqual(AlertStatus.RESOLVED.value, "resolved")
        self.assertEqual(AlertStatus.SUPPRESSED.value, "suppressed")
        self.assertEqual(AlertStatus.ACKNOWLEDGED.value, "acknowledged")

        # 测试AlertChannel
        self.assertEqual(AlertChannel.LOG.value, "log")
        self.assertEqual(AlertChannel.CONSOLE.value, "console")

    def test_health_status_enum(self):
        """测试健康状态枚举"""
        from miniboot.starters.actuator.monitoring import HealthStatus

        self.assertEqual(HealthStatus.UP.value, "UP")
        self.assertEqual(HealthStatus.DOWN.value, "DOWN")
        self.assertEqual(HealthStatus.OUT_OF_SERVICE.value, "OUT_OF_SERVICE")
        self.assertEqual(HealthStatus.UNKNOWN.value, "UNKNOWN")

    def test_monitoring_module_structure(self):
        """测试监控模块结构"""
        import miniboot.starters.actuator.monitoring as monitoring_module

        # 验证模块包含预期的组件
        expected_components = [
            'Alert', 'AlertChannel', 'AlertNotifier', 'AlertRule',
            'AlertSeverity', 'AlertStats', 'AlertStatus', 'MonitoringAlerts',
            'HealthChangeEvent', 'HealthMonitor', 'HealthSnapshot', 'HealthStatus',
            'MonitoringAlertsEndpoint'
        ]

        for component in expected_components:
            self.assertTrue(hasattr(monitoring_module, component),
                          f"监控模块缺少组件: {component}")

    def test_import_paths_updated(self):
        """测试导入路径已正确更新"""
        # 验证可以从新路径导入
        try:
            from miniboot.starters.actuator.monitoring.alerts import \
                MonitoringAlerts
            from miniboot.starters.actuator.monitoring.health import \
                HealthMonitor

            # 如果能成功导入，说明路径正确
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"导入路径更新失败: {e}")

    def test_cross_module_dependencies(self):
        """测试模块间依赖关系"""
        from miniboot.starters.actuator.monitoring.alerts import \
            MonitoringAlerts
        from miniboot.starters.actuator.monitoring.health import HealthMonitor

        # 验证HealthMonitor可以与MonitoringAlerts协作
        health_monitor = HealthMonitor()
        self.assertIsNotNone(health_monitor.alert_system)
        self.assertIsInstance(health_monitor.alert_system, MonitoringAlerts)




if __name__ == '__main__':
    unittest.main()
