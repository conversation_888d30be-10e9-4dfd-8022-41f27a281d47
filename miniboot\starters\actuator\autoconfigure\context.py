#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
应用上下文模块指标采集自动配置

实现应用上下文模块的指标采集功能，包括：
- 启动性能统计：14步启动序列各阶段耗时
- 模块加载状态：各核心模块初始化状态和耗时
- 生命周期事件：启动、停止、刷新事件统计
- 智能异步支持：异步环境检测和切换统计
- 应用健康状态：上下文状态、错误统计

配置条件：
- starters.actuator.metrics.core-modules.context=true (默认启用)
- 依赖 ActuatorStarterAutoConfiguration 已配置

使用示例：
    # application.yml
    starters:
        actuator:
            metrics:
                core-modules:
                    context: true  # 启用应用上下文指标采集
"""

import threading
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set

from loguru import logger

from miniboot.annotations.conditional import (ConditionalOnBean,
                                              ConditionalOnProperty)
from miniboot.annotations.core import Bean, Configuration
from miniboot.autoconfigure.base import AutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.monitoring.interfaces import MetricsCollector, MetricsData


@dataclass
class ContextMetrics:
    """应用上下文指标数据"""

    # 启动性能统计
    total_startup_time: float = 0.0
    startup_sequence_times: Dict[str, float] = field(default_factory=dict)
    startup_count: int = 0
    startup_failures: int = 0

    # 模块加载状态
    total_modules: int = 0
    initialized_modules: int = 0
    running_modules: int = 0
    failed_modules: int = 0
    module_load_times: Dict[str, float] = field(default_factory=dict)

    # 生命周期事件统计
    lifecycle_events: Dict[str, int] = field(default_factory=lambda: {
        'startup_events': 0,
        'stop_events': 0,
        'refresh_events': 0,
        'shutdown_events': 0
    })

    # 应用上下文状态
    current_state: str = "STOPPED"
    uptime: float = 0.0
    last_startup_time: Optional[float] = None
    last_shutdown_time: Optional[float] = None

    # Bean 生命周期统计
    total_beans: int = 0
    singleton_beans: int = 0
    prototype_beans: int = 0
    bean_creation_failures: int = 0

    # 事件发布统计
    published_events: int = 0
    failed_events: int = 0
    event_subscribers: int = 0
    async_events: int = 0

    # 异步环境统计
    async_mode_enabled: bool = False
    async_mode_detected: bool = False
    async_operations: int = 0
    async_failures: int = 0

    # 错误统计
    context_errors: int = 0
    configuration_errors: int = 0
    dependency_errors: int = 0

    def calculate_derived_metrics(self):
        """计算派生指标"""
        # 计算启动成功率
        if self.startup_count > 0:
            self.startup_success_rate = (self.startup_count - self.startup_failures) / self.startup_count
        else:
            self.startup_success_rate = 0.0

        # 计算模块初始化成功率
        if self.total_modules > 0:
            self.module_success_rate = self.initialized_modules / self.total_modules
        else:
            self.module_success_rate = 0.0

        # 计算事件发布成功率
        total_events = self.published_events + self.failed_events
        if total_events > 0:
            self.event_success_rate = self.published_events / total_events
        else:
            self.event_success_rate = 0.0


class ContextMetricsCollector(MetricsCollector):
    """应用上下文指标采集器

    负责收集应用上下文的各种性能指标和状态信息。
    通过监控启动序列、模块加载、生命周期事件等过程，
    提供详细的应用上下文性能分析数据。
    """

    def __init__(self):
        """初始化上下文指标采集器"""
        self._metrics = ContextMetrics()
        self._lock = threading.RLock()
        self._start_time = time.time()

        # 监控的应用上下文实例
        self._monitored_contexts: List[Any] = []

        # 监控的模块初始化器
        self._monitored_module_initializers: List[Any] = []

        # 监控的事件发布器
        self._monitored_event_publishers: List[Any] = []

        # 监控的Bean工厂
        self._monitored_bean_factories: List[Any] = []

        logger.info("ContextMetricsCollector initialized")

    def register_application_context(self, context: Any) -> None:
        """注册要监控的应用上下文

        Args:
            context: 应用上下文实例 (DefaultApplicationContext)
        """
        with self._lock:
            if context not in self._monitored_contexts:
                self._monitored_contexts.append(context)
                logger.debug(f"Registered application context for monitoring: {context}")

    def register_module_initializer(self, initializer: Any) -> None:
        """注册要监控的模块初始化器

        Args:
            initializer: 模块初始化器实例
        """
        with self._lock:
            if initializer not in self._monitored_module_initializers:
                self._monitored_module_initializers.append(initializer)
                logger.debug(f"Registered module initializer for monitoring: {initializer}")

    def register_event_publisher(self, publisher: Any) -> None:
        """注册要监控的事件发布器

        Args:
            publisher: 事件发布器实例
        """
        with self._lock:
            if publisher not in self._monitored_event_publishers:
                self._monitored_event_publishers.append(publisher)
                logger.debug(f"Registered event publisher for monitoring: {publisher}")

    def register_bean_factory(self, factory: Any) -> None:
        """注册要监控的Bean工厂

        Args:
            factory: Bean工厂实例
        """
        with self._lock:
            if factory not in self._monitored_bean_factories:
                self._monitored_bean_factories.append(factory)
                logger.debug(f"Registered bean factory for monitoring: {factory}")

    async def collect_metrics(self) -> ContextMetrics:
        """收集当前的应用上下文指标

        Returns:
            ContextMetrics: 当前的指标数据
        """
        import asyncio

        # 使用同步锁保护的内部函数
        def _collect_with_lock():
            with self._lock:
                # 重置指标
                metrics = ContextMetrics()

                # 收集应用上下文指标
                for context in self._monitored_contexts:
                    self._collect_context_metrics(context, metrics)

                # 收集模块初始化器指标
                for initializer in self._monitored_module_initializers:
                    self._collect_module_metrics(initializer, metrics)

                # 收集事件发布器指标
                for publisher in self._monitored_event_publishers:
                    self._collect_event_metrics(publisher, metrics)

                # 收集Bean工厂指标
                for factory in self._monitored_bean_factories:
                    self._collect_bean_metrics(factory, metrics)

                # 计算派生指标
                metrics.calculate_derived_metrics()

                # 更新内部指标
                self._metrics = metrics

                logger.debug(f"Collected context metrics: {metrics}")
                return metrics

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _collect_with_lock)

    def _collect_context_metrics(self, context: Any, metrics: ContextMetrics) -> None:
        """收集应用上下文指标"""
        try:
            # 获取应用状态
            if hasattr(context, '_state') and context._state:
                metrics.current_state = context._state.value

            # 获取运行状态
            if hasattr(context, 'is_running'):
                if context.is_running():
                    metrics.uptime = context._calculate_uptime() if hasattr(context, '_calculate_uptime') else 0.0

            # 获取启动时间
            if hasattr(context, '_startup_time') and context._startup_time:
                metrics.last_startup_time = context._startup_time

            # 获取应用状态详情
            if hasattr(context, 'get_application_status'):
                status = context.get_application_status()
                if 'application' in status:
                    app_status = status['application']
                    metrics.uptime = app_status.get('uptime', 0.0) or 0.0

                if 'beans' in status:
                    bean_status = status['beans']
                    metrics.total_beans = bean_status.get('total_registered', 0)
                    metrics.singleton_beans = bean_status.get('singleton_count', 0)

                if 'runtime' in status:
                    runtime_status = status['runtime']
                    metrics.async_mode_enabled = runtime_status.get('async_mode', False)
                    metrics.async_mode_detected = runtime_status.get('async_mode_detected', False)

        except Exception as e:
            logger.debug(f"Failed to collect context metrics: {e}")
            metrics.context_errors += 1

    def _collect_module_metrics(self, initializer: Any, metrics: ContextMetrics) -> None:
        """收集模块初始化器指标"""
        try:
            if hasattr(initializer, 'get_module_status'):
                module_status = initializer.get_module_status()

                metrics.total_modules = module_status.get('total_modules', 0)

                initialized_modules = module_status.get('initialized_modules', set())
                metrics.initialized_modules = len(initialized_modules) if isinstance(initialized_modules, (set, list)) else 0

                running_modules = module_status.get('running_modules', set())
                metrics.running_modules = len(running_modules) if isinstance(running_modules, (set, list)) else 0

                failed_modules = module_status.get('failed_modules', set())
                metrics.failed_modules = len(failed_modules) if isinstance(failed_modules, (set, list)) else 0

        except Exception as e:
            logger.debug(f"Failed to collect module metrics: {e}")

    def _collect_event_metrics(self, publisher: Any, metrics: ContextMetrics) -> None:
        """收集事件发布器指标"""
        try:
            if hasattr(publisher, '_stats'):
                stats = publisher._stats
                metrics.published_events += stats.get('published_events', 0)
                metrics.failed_events += stats.get('failed_events', 0)
                metrics.async_events += stats.get('async_events', 0)

            if hasattr(publisher, '_handlers'):
                metrics.event_subscribers += len(publisher._handlers)

        except Exception as e:
            logger.debug(f"Failed to collect event metrics: {e}")

    def _collect_bean_metrics(self, factory: Any, metrics: ContextMetrics) -> None:
        """收集Bean工厂指标"""
        try:
            if hasattr(factory, 'get_singleton_names'):
                singleton_names = factory.get_singleton_names()
                metrics.singleton_beans += len(singleton_names) if singleton_names else 0

            if hasattr(factory, '_performance_stats'):
                perf_stats = factory._performance_stats
                metrics.bean_creation_failures += perf_stats.get('creation_failures', 0)

        except Exception as e:
            logger.debug(f"Failed to collect bean metrics: {e}")

    async def get_metrics_dict(self) -> Dict[str, Any]:
        """获取指标的字典表示

        Returns:
            Dict[str, Any]: 指标字典
        """
        metrics = await self.collect_metrics_async()
        return {
            'startup_performance': {
                'total_startup_time': metrics.total_startup_time,
                'startup_count': metrics.startup_count,
                'startup_failures': metrics.startup_failures,
                'startup_success_rate': getattr(metrics, 'startup_success_rate', 0.0),
                'last_startup_time': metrics.last_startup_time,
            },
            'module_status': {
                'total_modules': metrics.total_modules,
                'initialized_modules': metrics.initialized_modules,
                'running_modules': metrics.running_modules,
                'failed_modules': metrics.failed_modules,
                'module_success_rate': getattr(metrics, 'module_success_rate', 0.0),
            },
            'lifecycle_events': metrics.lifecycle_events,
            'application_state': {
                'current_state': metrics.current_state,
                'uptime': metrics.uptime,
                'last_shutdown_time': metrics.last_shutdown_time,
            },
            'bean_lifecycle': {
                'total_beans': metrics.total_beans,
                'singleton_beans': metrics.singleton_beans,
                'prototype_beans': metrics.prototype_beans,
                'bean_creation_failures': metrics.bean_creation_failures,
            },
            'event_publishing': {
                'published_events': metrics.published_events,
                'failed_events': metrics.failed_events,
                'event_subscribers': metrics.event_subscribers,
                'async_events': metrics.async_events,
                'event_success_rate': getattr(metrics, 'event_success_rate', 0.0),
            },
            'async_environment': {
                'async_mode_enabled': metrics.async_mode_enabled,
                'async_mode_detected': metrics.async_mode_detected,
                'async_operations': metrics.async_operations,
                'async_failures': metrics.async_failures,
            },
            'error_statistics': {
                'context_errors': metrics.context_errors,
                'configuration_errors': metrics.configuration_errors,
                'dependency_errors': metrics.dependency_errors,
            }
        }

    def reset_metrics(self) -> None:
        """重置所有指标"""
        with self._lock:
            self._metrics = ContextMetrics()
            self._start_time = time.time()
            logger.info("Context metrics reset")

    # MetricsCollector 接口实现
    def collect_metrics(self) -> List[MetricsData]:
        """收集指标数据 - MetricsCollector 接口实现

        Returns:
            List[MetricsData]: 指标数据列表
        """
        import asyncio
        try:
            # 尝试在现有事件循环中运行
            loop = asyncio.get_running_loop()
            metrics = asyncio.run_coroutine_threadsafe(self.collect_metrics_async(), loop).result(timeout=5.0)
        except RuntimeError:
            # 没有运行中的事件循环，创建新的
            metrics = asyncio.run(self.collect_metrics_async())

        # 将 ContextMetrics 转换为 MetricsData 列表
        metrics_data = []

        # 启动指标
        metrics_data.append(MetricsData(
            name="context.startup_time",
            value=metrics.startup_time,
            unit="seconds",
            tags={"module": "context"},
            timestamp=time.time()
        ))

        # 模块指标
        metrics_data.append(MetricsData(
            name="context.modules_loaded",
            value=metrics.modules_loaded,
            unit="count",
            tags={"module": "context", "type": "modules"},
            timestamp=time.time()
        ))

        # Bean 指标
        metrics_data.append(MetricsData(
            name="context.singleton_beans",
            value=metrics.singleton_beans,
            unit="count",
            tags={"module": "context", "type": "beans"},
            timestamp=time.time()
        ))

        # 事件指标
        metrics_data.append(MetricsData(
            name="context.published_events",
            value=metrics.published_events,
            unit="count",
            tags={"module": "context", "type": "events"},
            timestamp=time.time()
        ))

        return metrics_data

    def get_collector_name(self) -> str:
        """获取收集器名称 - MetricsCollector 接口实现

        Returns:
            str: 收集器名称
        """
        return "context-metrics-collector"

    def get_supported_metrics(self) -> List[str]:
        """获取支持的指标名称列表 - MetricsCollector 接口实现

        Returns:
            List[str]: 支持的指标名称
        """
        return [
            "context.startup_time",
            "context.modules_loaded",
            "context.singleton_beans",
            "context.published_events",
            "context.async_operations",
            "context.configuration_errors"
        ]

    def is_available(self) -> bool:
        """检查收集器是否可用 - MetricsCollector 接口实现

        Returns:
            bool: 是否可用
        """
        # Context 指标收集器总是可用的，即使没有监控特定上下文
        # 它可以提供基本的上下文统计信息
        return True

    async def collect_metrics_async(self) -> ContextMetrics:
        """异步收集指标的内部方法（重命名原来的 collect_metrics）"""
        import asyncio

        def _collect_with_lock() -> ContextMetrics:
            with self._lock:
                metrics = ContextMetrics()

                # 收集应用上下文指标
                for context in self._monitored_contexts:
                    self._collect_context_metrics(context, metrics)

                # 收集事件发布器指标
                for publisher in self._monitored_event_publishers:
                    self._collect_event_metrics(publisher, metrics)

                # 收集Bean工厂指标
                for factory in self._monitored_bean_factories:
                    self._collect_bean_metrics(factory, metrics)

                # 计算派生指标
                metrics.calculate_derived_metrics()

                # 更新内部指标
                self._metrics = metrics

                logger.debug(f"Collected Context metrics: {metrics}")
                return metrics

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _collect_with_lock)


@ConditionalOnProperty(name="miniboot.starters.actuator.metrics.core-modules.context", match_if_missing=True)
@ConditionalOnBean(name="actuator_context")
class ContextMetricsAutoConfiguration(AutoConfiguration):
    """应用上下文模块指标采集自动配置类

    当满足以下条件时自动配置上下文指标采集：
    1. starters.actuator.metrics.core-modules.context=true (默认启用)
    2. ActuatorStarterAutoConfiguration 已配置

    注册的 Bean：
    - context_metrics_collector: 应用上下文指标采集器
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="context-metrics-auto-configuration",
            description="应用上下文模块指标采集自动配置",
            priority=200,  # 中等优先级
            auto_configure_after=["actuator-starter-auto-configuration"],
        )

    @Bean
    def context_metrics_collector(self) -> ContextMetricsCollector:
        """创建应用上下文指标采集器 Bean

        Returns:
            ContextMetricsCollector: 应用上下文指标采集器实例
        """
        collector = ContextMetricsCollector()
        logger.debug("Created ContextMetricsCollector bean")
        return collector
