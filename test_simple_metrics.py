#!/usr/bin/env python3
"""
简单的指标收集测试
验证 Actuator 模块的指标收集器是否正常工作
"""

import asyncio
import sys
import os
import time

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath('.'))

from miniboot.context.application import DefaultApplicationContext
from miniboot.monitoring.discovery import MonitoringComponentDiscovery


async def test_simple_metrics():
    """简单的指标收集测试"""
    print("🧪 Simple Metrics Collection Test")
    print("=" * 40)
    
    # 创建应用上下文
    context = DefaultApplicationContext(
        config_path="resources/application.yml",
        packages_to_scan=["miniboot.starters.actuator"]
    )
    
    try:
        # 启动应用上下文
        print("🚀 Starting application context...")
        await context.start()
        print("✅ Application context started")
        
        # 等待一下让组件完全初始化
        await asyncio.sleep(1)
        
        # 获取监控组件发现器
        discovery = MonitoringComponentDiscovery(context)
        
        # 发现指标收集器
        print("\n🔍 Discovering metrics collectors...")
        collectors = discovery.discover_metrics_collectors()
        print(f"📊 Found {len(collectors)} metrics collectors")
        
        # 测试每个收集器
        total_metrics = 0
        for i, collector in enumerate(collectors, 1):
            print(f"\n{i}. {collector.get_collector_name()}")
            print(f"   Available: {collector.is_available()}")
            print(f"   Supported metrics: {len(collector.get_supported_metrics())}")
            
            if collector.is_available():
                try:
                    # 直接调用异步方法（如果存在）
                    if hasattr(collector, 'collect_metrics_async'):
                        print(f"   🔄 Testing async collection...")
                        async_metrics = await collector.collect_metrics_async()
                        print(f"   ✅ Async collection successful: {type(async_metrics).__name__}")
                    
                    # 测试接口方法
                    print(f"   🔄 Testing interface collection...")
                    metrics_data = collector.collect_metrics()
                    print(f"   ✅ Interface collection successful: {len(metrics_data)} metrics")
                    total_metrics += len(metrics_data)
                    
                    # 显示前2个指标作为示例
                    for j, metric in enumerate(metrics_data[:2]):
                        print(f"     • {metric.name}: {metric.value} {metric.unit}")
                    
                except Exception as e:
                    print(f"   ❌ Collection failed: {e}")
                    import traceback
                    traceback.print_exc()
        
        print(f"\n📊 Summary:")
        print(f"   Total collectors: {len(collectors)}")
        print(f"   Total metrics collected: {total_metrics}")
        
        if total_metrics > 0:
            print("✅ Metrics collection is working!")
        else:
            print("⚠️ No metrics were collected")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 停止应用上下文
        print("\n🛑 Stopping application context...")
        await context.stop()
        print("✅ Application context stopped")


def main():
    """主函数"""
    try:
        asyncio.run(test_simple_metrics())
        return 0
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
