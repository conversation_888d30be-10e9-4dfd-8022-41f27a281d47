#!/usr/bin/env python3
"""
Actuator 详细分析测试
深入分析 Actuator 模块的具体问题和解决方案
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from miniboot.context.application import DefaultApplicationContext
from miniboot.env.environment import StandardEnvironment

async def analyze_health_endpoint_issue():
    """分析健康端点问题"""
    print("🔍 Analyzing Health Endpoint Issue...")
    
    try:
        # 创建环境
        environment = StandardEnvironment()
        environment.set_active_profiles('dev')
        
        # 创建应用上下文
        context = DefaultApplicationContext()
        context._environment = environment
        
        # 执行自动配置
        await context._execute_auto_configurations()
        
        # 获取健康端点
        health_endpoint = context.get_bean_factory().get('health_endpoint_provider')
        print(f"  📦 Health endpoint found: {health_endpoint is not None}")
        print(f"  📦 Health endpoint type: {type(health_endpoint).__name__}")
        
        if health_endpoint:
            # 检查端点方法
            print(f"  📊 Has handle_request: {hasattr(health_endpoint, 'handle_request')}")
            print(f"  📊 Has health method: {hasattr(health_endpoint, 'health')}")
            print(f"  📊 Has is_enabled: {hasattr(health_endpoint, 'is_enabled')}")
            
            # 尝试调用健康检查
            try:
                if hasattr(health_endpoint, 'health'):
                    health_result = await health_endpoint.health()
                    print(f"  ✅ Health check result: {health_result}")
                else:
                    print("  ❌ No health method found")
                    
                # 尝试调用 handle_request
                if hasattr(health_endpoint, 'handle_request'):
                    request_result = await health_endpoint.handle_request()
                    print(f"  ✅ Handle request result: {request_result}")
                else:
                    print("  ❌ No handle_request method found")
                    
            except Exception as e:
                print(f"  ❌ Error calling health methods: {e}")
                import traceback
                traceback.print_exc()
        
        return health_endpoint is not None
        
    except Exception as e:
        print(f"  ❌ Error analyzing health endpoint: {e}")
        import traceback
        traceback.print_exc()
        return False

async def analyze_actuator_context_issue():
    """分析 ActuatorContext 问题"""
    print("\n🔍 Analyzing ActuatorContext Issue...")
    
    try:
        # 创建环境
        environment = StandardEnvironment()
        environment.set_active_profiles('dev')
        
        # 创建应用上下文
        context = DefaultApplicationContext()
        context._environment = environment
        
        # 执行自动配置
        await context._execute_auto_configurations()
        
        # 获取 ActuatorContext
        actuator_context = context.get_bean_factory().get('monitoring_context')
        print(f"  📦 ActuatorContext found: {actuator_context is not None}")
        print(f"  📦 ActuatorContext type: {type(actuator_context).__name__}")
        
        if actuator_context:
            # 检查 ActuatorContext 状态
            print(f"  📊 Is started: {actuator_context.is_started}")
            print(f"  📊 Properties enabled: {actuator_context.properties.enabled}")
            
            # 检查端点注册
            endpoints = actuator_context.get_endpoints()
            print(f"  📊 Registered endpoints count: {len(endpoints)}")
            
            for endpoint_id, endpoint in endpoints.items():
                print(f"    📌 {endpoint_id}: {type(endpoint).__name__}")
            
            # 尝试手动注册健康端点
            health_endpoint = context.get_bean_factory().get('health_endpoint_provider')
            if health_endpoint and len(endpoints) == 0:
                print("  🔧 Attempting manual endpoint registration...")
                try:
                    actuator_context.register_endpoint('health', health_endpoint)
                    endpoints_after = actuator_context.get_endpoints()
                    print(f"  📊 Endpoints after manual registration: {len(endpoints_after)}")
                except Exception as e:
                    print(f"  ❌ Manual registration failed: {e}")
            
            # 检查启动状态
            if not actuator_context.is_started:
                print("  🔧 Attempting to start ActuatorContext...")
                try:
                    await actuator_context.start()
                    print(f"  📊 Started successfully: {actuator_context.is_started}")
                except Exception as e:
                    print(f"  ❌ Start failed: {e}")
        
        return actuator_context is not None and len(actuator_context.get_endpoints()) > 0
        
    except Exception as e:
        print(f"  ❌ Error analyzing ActuatorContext: {e}")
        import traceback
        traceback.print_exc()
        return False

async def analyze_monitoring_integration():
    """分析监控集成问题"""
    print("\n🔍 Analyzing Monitoring Integration...")
    
    try:
        # 创建环境
        environment = StandardEnvironment()
        environment.set_active_profiles('dev')
        
        # 创建应用上下文
        context = DefaultApplicationContext()
        context._environment = environment
        
        # 执行自动配置
        await context._execute_auto_configurations()
        
        # 检查监控集成结果
        print("  📊 Monitoring integration analysis:")
        
        # 检查发现的组件
        monitoring_context = context.get_bean_factory().get('monitoring_context')
        health_endpoint = context.get_bean_factory().get('health_endpoint_provider')
        info_endpoint = context.get_bean_factory().get('info_endpoint_provider')
        
        print(f"    📌 MonitoringContext: {monitoring_context is not None}")
        print(f"    📌 HealthEndpoint: {health_endpoint is not None}")
        print(f"    📌 InfoEndpoint: {info_endpoint is not None}")
        
        # 检查端点注册状态
        if monitoring_context:
            endpoints = monitoring_context.get_endpoints()
            print(f"    📌 Registered endpoints: {len(endpoints)}")
            
            # 如果没有端点，尝试手动集成
            if len(endpoints) == 0:
                print("  🔧 Attempting manual integration...")
                
                # 手动注册健康端点
                if health_endpoint:
                    try:
                        monitoring_context.register_endpoint('health', health_endpoint)
                        print("    ✅ Health endpoint registered manually")
                    except Exception as e:
                        print(f"    ❌ Failed to register health endpoint: {e}")
                
                # 手动注册信息端点
                if info_endpoint:
                    try:
                        monitoring_context.register_endpoint('info', info_endpoint)
                        print("    ✅ Info endpoint registered manually")
                    except Exception as e:
                        print(f"    ❌ Failed to register info endpoint: {e}")
                
                # 检查手动注册后的状态
                endpoints_after = monitoring_context.get_endpoints()
                print(f"    📌 Endpoints after manual registration: {len(endpoints_after)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error analyzing monitoring integration: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_endpoint_functionality():
    """测试端点功能"""
    print("\n🔍 Testing Endpoint Functionality...")
    
    try:
        # 创建环境
        environment = StandardEnvironment()
        environment.set_active_profiles('dev')
        
        # 创建应用上下文
        context = DefaultApplicationContext()
        context._environment = environment
        
        # 执行自动配置
        await context._execute_auto_configurations()
        
        # 获取端点
        health_endpoint = context.get_bean_factory().get('health_endpoint_provider')
        info_endpoint = context.get_bean_factory().get('info_endpoint_provider')
        
        success_count = 0
        total_tests = 0
        
        # 测试健康端点
        if health_endpoint:
            print("  📊 Testing Health Endpoint:")
            total_tests += 1
            
            try:
                # 测试健康检查方法
                if hasattr(health_endpoint, 'health'):
                    health_data = await health_endpoint.health()
                    print(f"    ✅ Health data: {health_data}")
                    success_count += 1
                else:
                    print("    ❌ No health method")
            except Exception as e:
                print(f"    ❌ Health check failed: {e}")
        
        # 测试信息端点
        if info_endpoint:
            print("  📊 Testing Info Endpoint:")
            total_tests += 1
            
            try:
                # 测试信息获取方法
                if hasattr(info_endpoint, 'get_info'):
                    info_data = await info_endpoint.get_info()
                    print(f"    ✅ Info data: {info_data}")
                    success_count += 1
                elif hasattr(info_endpoint, 'handle_request'):
                    info_data = await info_endpoint.handle_request()
                    print(f"    ✅ Info data (via handle_request): {info_data}")
                    success_count += 1
                else:
                    print("    ❌ No info method")
            except Exception as e:
                print(f"    ❌ Info check failed: {e}")
        
        print(f"  📊 Endpoint functionality test: {success_count}/{total_tests} successful")
        return success_count == total_tests and total_tests > 0
        
    except Exception as e:
        print(f"  ❌ Error testing endpoint functionality: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主分析函数"""
    print("🎯 Actuator Detailed Analysis")
    print("=" * 60)
    
    # 分析 1: 健康端点问题
    health_ok = await analyze_health_endpoint_issue()
    
    # 分析 2: ActuatorContext 问题
    context_ok = await analyze_actuator_context_issue()
    
    # 分析 3: 监控集成问题
    integration_ok = await analyze_monitoring_integration()
    
    # 分析 4: 端点功能测试
    functionality_ok = await test_endpoint_functionality()
    
    # 总结
    print("\n📊 Analysis Results Summary:")
    print("=" * 60)
    print(f"{'✅' if health_ok else '❌'} Health Endpoint Analysis: {'OK' if health_ok else 'ISSUES'}")
    print(f"{'✅' if context_ok else '❌'} ActuatorContext Analysis: {'OK' if context_ok else 'ISSUES'}")
    print(f"{'✅' if integration_ok else '❌'} Monitoring Integration: {'OK' if integration_ok else 'ISSUES'}")
    print(f"{'✅' if functionality_ok else '❌'} Endpoint Functionality: {'OK' if functionality_ok else 'ISSUES'}")
    
    # 判断整体结果
    overall_success = health_ok and context_ok and integration_ok and functionality_ok
    
    print(f"\n🎯 Overall Analysis: {'✅ ALL GOOD' if overall_success else '❌ NEEDS FIXES'}")
    
    if not overall_success:
        print("\n🔧 Recommended Actions:")
        if not health_ok:
            print("  - Fix health endpoint method implementation")
        if not context_ok:
            print("  - Fix ActuatorContext endpoint registration")
        if not integration_ok:
            print("  - Fix monitoring component integration")
        if not functionality_ok:
            print("  - Fix endpoint method implementations")

if __name__ == "__main__":
    asyncio.run(main())
