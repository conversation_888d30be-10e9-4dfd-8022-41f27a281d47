#!/usr/bin/env python3
"""
测试指标收集功能
验证 Actuator 模块的指标收集器是否正常工作
"""

import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath('.'))

from miniboot.context.application import DefaultApplicationContext
from miniboot.monitoring.discovery import MonitoringComponentDiscovery


async def test_metrics_collection():
    """测试指标收集功能"""
    print("🧪 Testing Metrics Collection...")

    # 创建应用上下文
    context = DefaultApplicationContext(
        config_path="resources/application.yml",
        packages_to_scan=["miniboot.starters.actuator"]
    )

    try:
        # 启动应用上下文
        print("🚀 Starting application context...")
        await context.start()
        print("✅ Application context started successfully")

        # 获取监控组件发现器
        discovery = MonitoringComponentDiscovery(context)

        # 发现指标收集器
        print("\n🔍 Discovering metrics collectors...")
        collectors = discovery.discover_metrics_collectors()
        print(f"📊 Found {len(collectors)} metrics collectors:")

        for collector in collectors:
            print(f"   - {collector.get_collector_name()}")
            print(f"     Available: {collector.is_available()}")
            print(f"     Supported metrics: {len(collector.get_supported_metrics())}")

            # 测试指标收集
            try:
                print(f"     🔄 Collecting metrics from {collector.get_collector_name()}...")
                metrics_data = collector.collect_metrics()
                print(f"     ✅ Collected {len(metrics_data)} metrics:")

                for metric in metrics_data[:3]:  # 只显示前3个指标
                    print(f"       • {metric.name}: {metric.value} {metric.unit}")

                if len(metrics_data) > 3:
                    print(f"       ... and {len(metrics_data) - 3} more metrics")

            except Exception as e:
                print(f"     ❌ Failed to collect metrics: {e}")

            print()

        # 测试聚合指标收集
        print("🔄 Testing aggregated metrics collection...")
        try:
            all_metrics = []
            for collector in collectors:
                if collector.is_available():
                    metrics = collector.collect_metrics()
                    all_metrics.extend(metrics)

            print(f"✅ Total aggregated metrics: {len(all_metrics)}")

            # 按模块分组显示
            modules = {}
            for metric in all_metrics:
                module = metric.tags.get('module', 'unknown')
                if module not in modules:
                    modules[module] = []
                modules[module].append(metric)

            print("📊 Metrics by module:")
            for module, metrics in modules.items():
                print(f"   {module}: {len(metrics)} metrics")

        except Exception as e:
            print(f"❌ Failed to collect aggregated metrics: {e}")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 停止应用上下文
        print("\n🛑 Stopping application context...")
        await context.stop()
        print("✅ Application context stopped")


def main():
    """主函数"""
    print("🧪 Mini-Boot Metrics Collection Test")
    print("=" * 50)

    try:
        asyncio.run(test_metrics_collection())
        print("\n✅ Test completed successfully!")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
