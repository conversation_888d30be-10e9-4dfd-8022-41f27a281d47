"""
Actuator 配置属性类测试

测试 ActuatorProperties 及相关配置类的功能，包括配置文件加载、验证、转换等。
"""

import unittest
from typing import Any, Dict
from unittest.mock import Mock, patch

from miniboot.starters.actuator.properties import (ActuatorConfigUtils,
                                                   ActuatorProperties,
                                                   CoreModulesProperties,
                                                   CustomMetricsProperties,
                                                   EndpointsProperties,
                                                   ExportProperties,
                                                   JsonExportProperties,
                                                   MetricsProperties,
                                                   NamingProperties,
                                                   PerformanceProperties,
                                                   PrometheusExportProperties,
                                                   SecurityProperties,
                                                   WebProperties)


class TestActuatorProperties(unittest.TestCase):
    """ActuatorProperties 测试"""

    def setUp(self):
        """测试前置设置"""
        self.properties = ActuatorProperties()

    def test_default_values(self):
        """测试默认配置值"""
        self.assertTrue(self.properties.enabled)
        self.assertTrue(self.properties.auto_start)
        self.assertTrue(self.properties.graceful_shutdown)
        self.assertEqual(self.properties.shutdown_timeout, 30)
        self.assertFalse(self.properties.debug)
        self.assertTrue(self.properties.performance_monitoring)

        # 测试嵌套对象默认值
        self.assertIsNotNone(self.properties.metrics)
        self.assertIsNotNone(self.properties.web)
        self.assertIsNotNone(self.properties.security)

    def test_feature_checks(self):
        """测试功能启用检查"""
        # 默认情况下应该启用
        self.assertTrue(self.properties.is_metrics_enabled())
        self.assertTrue(self.properties.is_web_enabled())
        self.assertTrue(self.properties.is_security_enabled())

        # 禁用主开关
        self.properties.enabled = False
        self.assertFalse(self.properties.is_metrics_enabled())
        self.assertFalse(self.properties.is_web_enabled())
        self.assertFalse(self.properties.is_security_enabled())

    def test_enabled_core_modules(self):
        """测试启用的核心模块"""
        modules = self.properties.get_enabled_core_modules()
        expected = ["bean", "scheduler", "context", "env"]
        self.assertEqual(modules, expected)

        # 禁用部分模块
        self.properties.metrics.core_modules.bean = False
        self.properties.metrics.core_modules.env = False

        modules = self.properties.get_enabled_core_modules()
        expected = ["scheduler", "context"]
        self.assertEqual(modules, expected)

    def test_enabled_endpoints(self):
        """测试启用的端点"""
        endpoints = self.properties.get_enabled_endpoints()
        expected = ["health", "info", "metrics", "beans", "env", "loggers", "threaddump"]
        self.assertEqual(endpoints, expected)

    def test_metrics_collection_interval(self):
        """测试指标收集间隔"""
        # 默认值
        interval = self.properties.get_metrics_collection_interval_seconds()
        self.assertEqual(interval, 30.0)  # 默认 "30s"

        # 禁用指标时应该返回 0
        self.properties.metrics.enabled = False
        interval = self.properties.get_metrics_collection_interval_seconds()
        self.assertEqual(interval, 0.0)

    def test_merged_common_tags(self):
        """测试合并的公共标签"""
        tags = self.properties.get_merged_common_tags()

        # 应该包含基础标签
        self.assertIn("application", tags)
        self.assertIn("version", tags)

        # 测试用户自定义标签合并
        self.properties.metrics.custom.common_tags = {"env": "test", "team": "backend"}
        tags = self.properties.get_merged_common_tags()

        self.assertEqual(tags["env"], "test")
        self.assertEqual(tags["team"], "backend")
        self.assertIn("application", tags)  # 基础标签仍然存在

    def test_to_dict(self):
        """测试转换为字典"""
        data = self.properties.to_dict()

        # 检查基本字段
        self.assertIn("enabled", data)
        self.assertIn("auto_start", data)
        self.assertIn("metrics_enabled", data)
        self.assertIn("web_enabled", data)
        self.assertIn("security_enabled", data)
        self.assertIn("enabled_core_modules", data)
        self.assertIn("enabled_endpoints", data)

        # 检查值的正确性
        self.assertTrue(data["enabled"])
        self.assertTrue(data["metrics_enabled"])


class TestMetricsProperties(unittest.TestCase):
    """MetricsProperties 测试"""

    def setUp(self):
        self.metrics = MetricsProperties()

    def test_default_values(self):
        """测试默认值"""
        self.assertTrue(self.metrics.enabled)
        self.assertEqual(self.metrics.collection_interval, "30s")
        self.assertTrue(self.metrics.cache_enabled)
        self.assertTrue(self.metrics.async_collection)
        self.assertEqual(self.metrics.batch_size, 100)

        # 测试嵌套对象
        self.assertIsNotNone(self.metrics.core_modules)
        self.assertIsNotNone(self.metrics.custom)
        self.assertEqual(len(self.metrics.custom_collectors), 0)


class TestWebProperties(unittest.TestCase):
    """WebProperties 测试"""

    def setUp(self):
        self.web = WebProperties()

    def test_default_values(self):
        """测试默认值"""
        self.assertTrue(self.web.enabled)
        self.assertEqual(self.web.base_path, "/actuator")
        self.assertIsNone(self.web.port)
        self.assertEqual(self.web.cors_allowed_origins, ["*"])
        self.assertTrue(self.web.expose_all_endpoints)

        # 测试嵌套对象
        self.assertIsNotNone(self.web.endpoints)


class TestActuatorConfigUtils(unittest.TestCase):
    """ActuatorConfigUtils 工具类测试"""

    def test_validate_endpoint_path(self):
        """测试端点路径验证"""
        # 有效路径
        self.assertTrue(ActuatorConfigUtils.validate_endpoint_path("/actuator"))
        self.assertTrue(ActuatorConfigUtils.validate_endpoint_path("/health"))
        self.assertTrue(ActuatorConfigUtils.validate_endpoint_path("/api/v1/metrics"))

        # 无效路径
        self.assertFalse(ActuatorConfigUtils.validate_endpoint_path(""))
        self.assertFalse(ActuatorConfigUtils.validate_endpoint_path("actuator"))  # 不以 / 开头
        self.assertFalse(ActuatorConfigUtils.validate_endpoint_path("/health?param=1"))  # 包含 ?
        self.assertFalse(ActuatorConfigUtils.validate_endpoint_path("/health#section"))  # 包含 #
        self.assertFalse(ActuatorConfigUtils.validate_endpoint_path("/health test"))  # 包含空格

    def test_merge_tags(self):
        """测试标签合并"""
        common_tags = {"app": "miniboot", "version": "1.0"}
        specific_tags = {"env": "test", "version": "1.1"}  # version 会被覆盖

        merged = ActuatorConfigUtils.merge_tags(common_tags, specific_tags)

        expected = {"app": "miniboot", "version": "1.1", "env": "test"}
        self.assertEqual(merged, expected)

        # 测试空字典
        merged = ActuatorConfigUtils.merge_tags({}, {"key": "value"})
        self.assertEqual(merged, {"key": "value"})

        merged = ActuatorConfigUtils.merge_tags({"key": "value"}, {})
        self.assertEqual(merged, {"key": "value"})


class TestConfigurationLoading(unittest.TestCase):
    """配置文件加载测试"""

    def test_create_from_environment_basic(self):
        """测试从环境创建基础配置"""
        # 模拟环境配置
        mock_env = Mock()
        mock_env.get_property.side_effect = lambda key, default=None: {
            "miniboot.starters.actuator.enabled": True,
            "miniboot.starters.actuator.auto-start": True,
            "miniboot.starters.actuator.metrics.enabled": True,
            "miniboot.starters.actuator.metrics.collection-interval": "60s",
            "miniboot.starters.actuator.metrics.batch-size": 200,
            "miniboot.starters.actuator.web.enabled": True,
            "miniboot.starters.actuator.web.base-path": "/management",
            "miniboot.starters.actuator.web.port": 8081,
        }.get(key, default)

        properties = ActuatorConfigUtils.create_from_environment(mock_env)

        # 验证基础配置
        self.assertTrue(properties.enabled)
        self.assertTrue(properties.auto_start)

        # 验证指标配置
        self.assertTrue(properties.metrics.enabled)
        self.assertEqual(properties.metrics.collection_interval, "60s")
        self.assertEqual(properties.metrics.batch_size, 200)

        # 验证 Web 配置
        self.assertTrue(properties.web.enabled)
        self.assertEqual(properties.web.base_path, "/management")
        self.assertEqual(properties.web.port, 8081)

    def test_create_from_real_environment(self):
        """测试从真实环境加载 application.yml 配置"""
        from miniboot.env.environment import StandardEnvironment

        # 创建真实的环境，会自动加载 application.yml 中的 starters.actuator 配置
        environment = StandardEnvironment()
        environment.set_active_profiles("dev")

        # 从环境创建 ActuatorProperties（直接使用 application.yml 中的配置）
        properties = ActuatorConfigUtils.create_from_environment(environment)

        # 验证从 application.yml 加载的配置
        self.assertIsNotNone(properties)
        self.assertIsInstance(properties, ActuatorProperties)

        # 验证 application.yml 中的基础配置
        self.assertTrue(properties.enabled)  # enabled: true
        self.assertTrue(properties.auto_start)  # auto-start: true

        # 验证 application.yml 中的指标配置
        self.assertIsNotNone(properties.metrics)
        self.assertTrue(properties.metrics.enabled)  # metrics.enabled: true
        self.assertTrue(properties.metrics.core_modules.bean)  # core-modules.bean: true
        self.assertTrue(properties.metrics.core_modules.scheduler)  # core-modules.scheduler: true
        self.assertTrue(properties.metrics.core_modules.context)  # core-modules.context: true
        self.assertTrue(properties.metrics.core_modules.env)  # core-modules.env: true
        self.assertTrue(properties.metrics.custom.enabled)  # custom.enabled: true
        self.assertEqual(properties.metrics.custom.naming.prefix, "miniboot")  # naming.prefix: "miniboot"

        # 验证 application.yml 中的 Web 配置
        self.assertIsNotNone(properties.web)
        self.assertTrue(properties.web.enabled)  # web.enabled: true
        self.assertEqual(properties.web.base_path, "/actuator")  # base-path: "/actuator"

        # 验证默认的安全配置（使用默认值）
        self.assertIsNotNone(properties.security)
        # 注意：security.enabled 在 application.yml 中没有配置，使用默认值 False

        # 验证默认的性能配置
        self.assertIsNotNone(properties.performance)

        # 验证环境中的实际属性值
        self.assertEqual(environment.get_property("miniboot.starters.actuator.enabled"), True)
        self.assertEqual(environment.get_property("miniboot.starters.actuator.auto-start"), True)
        self.assertEqual(environment.get_property("miniboot.starters.actuator.metrics.enabled"), True)
        self.assertEqual(environment.get_property("miniboot.starters.actuator.web.enabled"), True)

        print(f"✓ 成功从 application.yml 加载 ActuatorProperties:")
        print(f"  - enabled: {properties.enabled}")
        print(f"  - metrics.enabled: {properties.metrics.enabled}")
        print(f"  - web.enabled: {properties.web.enabled}")
        print(f"  - web.base_path: {properties.web.base_path}")
        print(f"  - security.enabled: {properties.security.enabled}")
        print(f"  - metrics.custom.naming.prefix: {properties.metrics.custom.naming.prefix}")

    def test_create_from_environment_with_defaults(self):
        """测试从环境创建配置（使用默认值）"""
        # 模拟空环境配置
        mock_env = Mock()
        mock_env.get_property.return_value = None  # 所有配置都返回 None，使用默认值

        properties = ActuatorConfigUtils.create_from_environment(mock_env)

        # 验证使用了默认值
        self.assertTrue(properties.enabled)
        self.assertTrue(properties.auto_start)
        self.assertEqual(properties.metrics.collection_interval, "30s")
        self.assertEqual(properties.metrics.batch_size, 100)
        self.assertEqual(properties.web.base_path, "/actuator")
        self.assertIsNone(properties.web.port)

    @patch('miniboot.starters.actuator.properties.ActuatorConfigUtils.merge_tags')
    def test_create_from_environment_with_tags(self, mock_merge_tags):
        """测试从环境创建配置（包含标签）"""
        mock_merge_tags.return_value = {"app": "test", "env": "dev"}

        mock_env = Mock()
        mock_env.get_property.side_effect = lambda key, default=None: {
            "miniboot.starters.actuator.metrics.custom.common-tags.app": "test",
            "miniboot.starters.actuator.metrics.custom.common-tags.env": "dev",
        }.get(key, default)

        properties = ActuatorConfigUtils.create_from_environment(mock_env)

        # 验证标签配置
        self.assertIsNotNone(properties.metrics.custom.common_tags)





if __name__ == '__main__':
    unittest.main()
