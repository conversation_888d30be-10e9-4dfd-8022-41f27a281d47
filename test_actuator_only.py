#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试只启用 Actuator 模块的框架启动
"""

import asyncio
import sys

sys.path.append('.')

from miniboot.context.application import DefaultApplicationContext


async def test_actuator_only_startup():
    print('🚀 Starting Mini-Boot Framework with only Actuator enabled...')

    try:
        # 创建应用上下文
        context = DefaultApplicationContext(
            config_path='resources/application.yml',
            packages_to_scan=['miniboot.starters.actuator'],
            auto_detect=True
        )

        # 启动应用
        await context.start()

        print('✅ Framework startup successful!')

        # 检查 Bean 工厂状态
        bean_factory = context.get_bean_factory()
        print(f'📊 Bean Factory Status:')
        try:
            # 尝试不同的属性名
            if hasattr(bean_factory, '_beans'):
                print(f'   - Total beans: {len(bean_factory._beans)}')
            elif hasattr(bean_factory, 'beans'):
                print(f'   - Total beans: {len(bean_factory.beans)}')
            else:
                print(f'   - Bean factory type: {type(bean_factory).__name__}')

            if hasattr(bean_factory, '_singleton_beans'):
                print(f'   - Singleton beans: {len(bean_factory._singleton_beans)}')
            elif hasattr(bean_factory, 'singleton_beans'):
                print(f'   - Singleton beans: {len(bean_factory.singleton_beans)}')
            else:
                print(f'   - Singleton beans: Unable to access')
        except Exception as e:
            print(f'   - Bean factory inspection failed: {e}')

        # 检查 Actuator 相关 Bean
        actuator_beans = [
            'actuator_context',
            'health_endpoint',
            'info_endpoint',
            'metrics_endpoint'
        ]

        print(f'🔍 Actuator Beans Status:')
        for bean_name in actuator_beans:
            try:
                bean = bean_factory.get(bean_name)
                print(f'   ✅ {bean_name}: {type(bean).__name__}')
            except Exception as e:
                print(f'   ❌ {bean_name}: {e}')

        # 测试 Actuator 端点
        print(f'🧪 Testing Actuator Endpoints:')
        try:
            actuator_context = bean_factory.get('actuator_context')
            endpoints = actuator_context.get_endpoints()
            print(f'   📋 Available endpoints: {list(endpoints.keys())}')

            # 测试健康端点
            if 'health' in endpoints:
                health_endpoint = endpoints['health']
                from miniboot.starters.actuator.endpoints.base import \
                    OperationType
                health_result = health_endpoint.invoke(OperationType.READ)
                print(f'   ✅ Health check: {health_result}')

        except Exception as e:
            print(f'   ❌ Actuator endpoint test failed: {e}')
            import traceback
            traceback.print_exc()

        # 关闭应用
        await context.stop()
        print('🛑 Framework shutdown successful!')

    except Exception as e:
        print(f'❌ Framework startup failed: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_actuator_only_startup())
