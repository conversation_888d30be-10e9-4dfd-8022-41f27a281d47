#!/usr/bin/env python
# encoding: utf-8
"""
完整框架启动测试 - 验证 Actuator 模块监控和数据采集功能

测试目标：
1. 完整框架启动（包括所有模块）
2. Actuator 模块正常工作
3. 监控指标采集正常
4. 健康检查功能正常
5. 端点访问正常
6. 性能监控正常
"""

import asyncio
import time
from datetime import datetime
from typing import Any, Dict

from miniboot.context.application import DefaultApplicationContext
from miniboot.env.environment import StandardEnvironment


class CompleteFrameworkTest:
    """完整框架测试类"""

    def __init__(self):
        self.context = None
        self.test_results = {
            "framework_startup": False,
            "actuator_enabled": False,
            "health_endpoint": False,
            "info_endpoint": False,
            "metrics_collection": False,
            "performance_monitoring": False,
            "endpoint_registration": False,
            "monitoring_integration": False
        }
        self.metrics_data = {}
        self.health_data = {}
        self.info_data = {}

    async def run_complete_test(self) -> Dict[str, Any]:
        """运行完整测试"""
        print("🚀 Starting Complete Framework Test...")
        print("=" * 60)

        try:
            # 1. 测试框架启动
            await self._test_framework_startup()

            # 2. 测试 Actuator 模块
            await self._test_actuator_module()

            # 3. 测试监控集成
            await self._test_monitoring_integration()

            # 4. 测试端点功能
            await self._test_endpoint_functionality()

            # 5. 测试指标采集
            await self._test_metrics_collection()

            # 6. 测试性能监控
            await self._test_performance_monitoring()

            # 7. 生成测试报告
            return self._generate_test_report()

        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            return {"error": str(e), "results": self.test_results, "success_rate": 0.0}
        finally:
            if self.context:
                try:
                    await self.context.stop()
                    print("🔄 Framework stopped successfully")
                except Exception as e:
                    print(f"⚠️ Error stopping framework: {e}")

    async def _test_framework_startup(self):
        """测试框架启动"""
        print("\n📋 1. Testing Framework Startup...")

        try:
            # 创建环境和上下文
            environment = StandardEnvironment()
            self.context = DefaultApplicationContext()

            # 设置环境到上下文
            self.context._environment = environment

            # 启动框架（使用新的自动配置系统）
            print("   🔧 Starting framework with new auto-configuration...")
            await self.context._execute_new_auto_configurations()

            # 检查框架状态
            bean_count = len(self.context.get_bean_factory().names())
            print(f"   ✅ Framework started successfully with {bean_count} beans")

            self.test_results["framework_startup"] = True

        except Exception as e:
            print(f"   ❌ Framework startup failed: {e}")
            raise

    async def _test_actuator_module(self):
        """测试 Actuator 模块"""
        print("\n📋 2. Testing Actuator Module...")

        try:
            # 检查 Actuator 相关 Bean
            bean_factory = self.context.get_bean_factory()

            # 检查 ActuatorProperties
            if bean_factory.contains("actuator_properties"):
                actuator_props = bean_factory.get("actuator_properties")
                print(f"   ✅ ActuatorProperties: enabled={actuator_props.enabled}")
                self.test_results["actuator_enabled"] = actuator_props.enabled

            # 检查 MonitoringContext
            if bean_factory.contains("monitoring_context"):
                monitoring_context = bean_factory.get("monitoring_context")
                endpoint_count = len(monitoring_context._endpoint_registry._endpoints)
                print(f"   ✅ MonitoringContext: {endpoint_count} endpoints registered")
                self.test_results["endpoint_registration"] = endpoint_count > 0

            # 检查端点提供者
            health_provider = bean_factory.get("health_endpoint_provider") if bean_factory.contains("health_endpoint_provider") else None
            info_provider = bean_factory.get("info_endpoint_provider") if bean_factory.contains("info_endpoint_provider") else None

            if health_provider:
                print(f"   ✅ Health endpoint provider: {health_provider.__class__.__name__}")
            if info_provider:
                print(f"   ✅ Info endpoint provider: {info_provider.__class__.__name__}")

        except Exception as e:
            print(f"   ❌ Actuator module test failed: {e}")
            raise

    async def _test_monitoring_integration(self):
        """测试监控集成"""
        print("\n📋 3. Testing Monitoring Integration...")

        try:
            # 检查监控组件发现
            from miniboot.monitoring.discovery import \
                MonitoringComponentDiscovery
            from miniboot.monitoring.interfaces import (EndpointProvider,
                                                        MonitoringContext)

            discovery = MonitoringComponentDiscovery(self.context)

            # 发现监控上下文
            contexts = discovery.discover_monitoring_contexts()
            print(f"   ✅ Discovered {len(contexts)} monitoring contexts")

            # 发现端点提供者
            providers = discovery.discover_endpoint_providers()
            print(f"   ✅ Discovered {len(providers)} endpoint providers")

            # 检查集成状态
            if len(contexts) > 0 and len(providers) > 0:
                self.test_results["monitoring_integration"] = True
                print("   ✅ Monitoring integration successful")
            else:
                print("   ⚠️ Monitoring integration incomplete")

        except Exception as e:
            print(f"   ❌ Monitoring integration test failed: {e}")
            raise

    async def _test_endpoint_functionality(self):
        """测试端点功能"""
        print("\n📋 4. Testing Endpoint Functionality...")

        try:
            bean_factory = self.context.get_bean_factory()

            # 测试健康端点
            if bean_factory.contains("health_endpoint_provider"):
                health_provider = bean_factory.get("health_endpoint_provider")
                if health_provider.is_enabled():
                    # 创建一个模拟请求对象
                    mock_request = {"method": "GET", "path": "/actuator/health"}
                    self.health_data = await health_provider.handle_request(mock_request)
                    print(f"   ✅ Health endpoint: {self.health_data.get('status', 'UNKNOWN')}")
                    self.test_results["health_endpoint"] = True

            # 测试信息端点
            if bean_factory.contains("info_endpoint_provider"):
                info_provider = bean_factory.get("info_endpoint_provider")
                if info_provider.is_enabled():
                    # 创建一个模拟请求对象
                    mock_request = {"method": "GET", "path": "/actuator/info"}
                    self.info_data = await info_provider.handle_request(mock_request)
                    print(f"   ✅ Info endpoint: {len(self.info_data)} properties")
                    self.test_results["info_endpoint"] = True

        except Exception as e:
            print(f"   ❌ Endpoint functionality test failed: {e}")
            raise

    async def _test_metrics_collection(self):
        """测试指标采集"""
        print("\n📋 5. Testing Metrics Collection...")

        try:
            bean_factory = self.context.get_bean_factory()
            metrics_collected = 0

            # 检查各种指标收集器
            collectors = [
                "context_metrics_collector",
                "bean_metrics_collector",
                "scheduler_metrics_collector",
                "env_metrics_collector"
            ]

            for collector_name in collectors:
                if bean_factory.contains(collector_name):
                    collector = bean_factory.get(collector_name)
                    if hasattr(collector, 'collect_metrics'):
                        try:
                            metrics = await collector.collect_metrics()
                            if metrics:
                                self.metrics_data[collector_name] = metrics
                                metrics_collected += 1
                                print(f"   ✅ {collector_name}: {len(metrics)} metrics")
                        except Exception as e:
                            print(f"   ⚠️ {collector_name}: {e}")

            if metrics_collected > 0:
                self.test_results["metrics_collection"] = True
                print(f"   ✅ Metrics collection successful: {metrics_collected} collectors active")
            else:
                print("   ⚠️ No metrics collected")

        except Exception as e:
            print(f"   ❌ Metrics collection test failed: {e}")
            raise

    async def _test_performance_monitoring(self):
        """测试性能监控"""
        print("\n📋 6. Testing Performance Monitoring...")

        try:
            bean_factory = self.context.get_bean_factory()

            # 检查性能指标注册表
            if bean_factory.contains("metrics_registry"):
                metrics_registry = bean_factory.get("metrics_registry")

                # 模拟一些性能操作
                start_time = time.time()

                # 执行一些操作来生成指标
                await asyncio.sleep(0.1)  # 模拟异步操作

                end_time = time.time()
                duration = end_time - start_time

                # 检查是否能记录指标
                if hasattr(metrics_registry, 'get_all_metrics'):
                    all_metrics = metrics_registry.get_all_metrics()
                    print(f"   ✅ Performance registry: {len(all_metrics)} metrics")
                    self.test_results["performance_monitoring"] = True
                else:
                    print("   ✅ Performance monitoring available")
                    self.test_results["performance_monitoring"] = True

        except Exception as e:
            print(f"   ❌ Performance monitoring test failed: {e}")
            raise

    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        print("\n📊 Test Report Summary:")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        success_rate = (passed_tests / total_tests) * 100

        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {status} {test_name.replace('_', ' ').title()}")

        print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% ({passed_tests}/{total_tests})")

        # 详细数据
        report = {
            "success_rate": success_rate,
            "passed_tests": passed_tests,
            "total_tests": total_tests,
            "test_results": self.test_results,
            "health_data": self.health_data,
            "info_data": self.info_data,
            "metrics_data": self.metrics_data,
            "timestamp": datetime.now().isoformat()
        }

        if success_rate >= 80:
            print("🎉 Framework and Actuator are working correctly!")
        else:
            print("⚠️ Some issues detected, please check the failed tests")

        return report


async def main():
    """主测试函数"""
    test = CompleteFrameworkTest()
    result = await test.run_complete_test()

    print(f"\n📋 Final Result: {result['success_rate']:.1f}% success rate")
    return result


if __name__ == "__main__":
    asyncio.run(main())
