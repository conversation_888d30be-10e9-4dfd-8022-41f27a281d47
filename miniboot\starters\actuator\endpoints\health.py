#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 异步健康检查端点 - 高性能异步实现

提供高性能的异步健康检查端点,支持并发执行健康检查、超时保护和智能缓存.

核心特性:
- 并发执行多个健康指标检查
- 超时保护防止长时间阻塞
- 智能缓存减少重复检查
- 实时性能监控
- 异步健康指标接口
"""

import asyncio
import time
from abc import ABC, abstractmethod
from datetime import datetime
# 定义 HealthStatus 枚举
from enum import Enum
from typing import Any, Dict, List, Optional

import psutil
from loguru import logger

# 导入监控接口
from miniboot.monitoring.interfaces import EndpointProvider
from miniboot.starters.actuator.endpoints.base import (Endpoint, EndpointInfo,
                                                       EndpointOperation,
                                                       OperationType)
from miniboot.utils import cached, timeout


class HealthStatus(Enum):
    """健康状态枚举"""

    UP = "UP"
    DOWN = "DOWN"
    UNKNOWN = "UNKNOWN"


class HealthIndicator:
    """健康指标基类"""

    def __init__(self, name: str):
        self.name = name

    def check_health(self) -> Dict[str, Any]:
        """检查健康状态"""
        return {"status": HealthStatus.UP.value, "details": {}, "timestamp": datetime.now().isoformat()}


class HealthEndpoint(Endpoint, EndpointProvider):
    """健康检查端点 - 同时实现 Endpoint 和 EndpointProvider 接口"""

    def __init__(self):
        # 初始化 Endpoint 基类
        super().__init__("health", enabled=True, sensitive=False)
        # 保持向后兼容
        self.endpoint_id = "health"
        self.indicators = {}



    async def health(self, detailed: bool = False) -> Dict[str, Any]:
        """获取健康状态"""
        return {"status": HealthStatus.UP.value, "timestamp": datetime.now().isoformat(), "details": {} if not detailed else {"components": {}}}

    # ==================== EndpointProvider 接口实现 ====================

    def get_endpoint_info(self) -> EndpointInfo:
        """获取端点信息 - EndpointProvider 接口实现"""
        return EndpointInfo(
            name="health",
            path="/actuator/health",
            methods=["GET"],
            description="Health check endpoint providing system health status"
        )

    async def handle_request(self, request: Any) -> Any:
        """处理端点请求 - EndpointProvider 接口实现"""
        try:
            # 使用现有的健康检查逻辑
            health_data = await self.health()
            return health_data
        except Exception as e:
            logger.error(f"Error handling health check request: {e}")
            return {
                "status": HealthStatus.DOWN.value,
                "details": {"error": str(e)},
                "timestamp": datetime.now().isoformat()
            }

    def is_enabled(self) -> bool:
        """检查端点是否启用 - EndpointProvider 接口实现"""
        return self.enabled

    # ==================== Endpoint 接口实现 ====================

    def operations(self) -> list[EndpointOperation]:
        """返回端点支持的操作列表 - Endpoint 接口实现"""
        return [
            EndpointOperation(
                operation_type=OperationType.READ,
                method="GET",
                path="/actuator/health"
            )
        ]

    def invoke(self, operation_type: OperationType, **kwargs) -> Any:
        """执行端点操作 - Endpoint 接口实现"""
        if operation_type == OperationType.READ:
            # 使用 asyncio.run 来执行异步方法
            import asyncio
            return asyncio.run(self.health())
        else:
            raise ValueError(f"Unsupported operation type: {operation_type}")


class AsyncHealthIndicator(ABC):
    """异步健康指标接口"""

    def __init__(self, name: str, timeout: float = 5.0):
        """初始化异步健康指标

        Args:
            name: 指标名称
            timeout: 检查超时时间(秒)
        """
        self.name = name
        self.timeout = timeout

    @abstractmethod
    async def health_async(self) -> Dict[str, Any]:
        """异步返回健康状态信息"""
        pass

    def health(self) -> Dict[str, Any]:
        """同步兼容方法"""
        try:
            loop = asyncio.get_running_loop()
            task = loop.create_task(self.health_async())
            return task
        except RuntimeError:
            return asyncio.run(self.health_async())


class AsyncDiskSpaceHealthIndicator(AsyncHealthIndicator):
    """异步磁盘空间健康指标"""

    def __init__(self, threshold_bytes: int = 10 * 1024 * 1024 * 1024, timeout: float = 3.0):
        """初始化异步磁盘空间健康指标

        Args:
            threshold_bytes: 磁盘空间阈值(字节)
            timeout: 检查超时时间(秒)
        """
        super().__init__("disk", timeout)
        self.threshold_bytes = threshold_bytes

    @timeout(3.0)
    async def health_async(self) -> Dict[str, Any]:
        """异步获取磁盘健康状态"""
        try:
            # 在线程池中执行同步 I/O 操作
            loop = asyncio.get_event_loop()
            disk_usage = await loop.run_in_executor(None, psutil.disk_usage, "/")

            free_bytes = disk_usage.free
            status = HealthStatus.UP if free_bytes > self.threshold_bytes else HealthStatus.DOWN

            return {
                "status": status.value,
                "details": {
                    "total": f"{disk_usage.total // (1024**3)}GB",
                    "free": f"{free_bytes // (1024**3)}GB",
                    "threshold": f"{self.threshold_bytes // (1024**3)}GB",
                    "usage_percent": f"{((disk_usage.total - free_bytes) / disk_usage.total * 100):.1f}%",
                },
                "timestamp": datetime.now().isoformat(),
            }
        except Exception as e:
            logger.error(f"Disk health check failed: {e}")
            return {"status": HealthStatus.DOWN.value, "details": {"error": str(e)}, "timestamp": datetime.now().isoformat()}


class AsyncMemoryHealthIndicator(AsyncHealthIndicator):
    """异步内存健康指标"""

    def __init__(self, threshold_percent: float = 90.0, timeout: float = 2.0):
        """初始化异步内存健康指标

        Args:
            threshold_percent: 内存使用率阈值(百分比)
            timeout: 检查超时时间(秒)
        """
        super().__init__("memory", timeout)
        self.threshold_percent = threshold_percent

    @timeout(2.0)
    async def health_async(self) -> Dict[str, Any]:
        """异步获取内存健康状态"""
        try:
            # 在线程池中执行同步 I/O 操作
            loop = asyncio.get_event_loop()
            memory = await loop.run_in_executor(None, psutil.virtual_memory)

            usage_percent = memory.percent
            status = HealthStatus.UP if usage_percent < self.threshold_percent else HealthStatus.DOWN

            return {
                "status": status.value,
                "details": {
                    "usage": f"{usage_percent:.1f}%",
                    "total": f"{memory.total // (1024**3)}GB",
                    "available": f"{memory.available // (1024**3)}GB",
                    "used": f"{memory.used // (1024**3)}GB",
                    "threshold": f"{self.threshold_percent}%",
                },
                "timestamp": datetime.now().isoformat(),
            }
        except Exception as e:
            logger.error(f"Memory health check failed: {e}")
            return {"status": HealthStatus.DOWN.value, "details": {"error": str(e)}, "timestamp": datetime.now().isoformat()}


class AsyncApplicationHealthIndicator(AsyncHealthIndicator):
    """异步应用健康指标"""

    def __init__(self, timeout: float = 1.0):
        """初始化异步应用健康指标"""
        super().__init__("application", timeout)
        self._start_time = time.time()

    @timeout(1.0)
    async def health_async(self) -> Dict[str, Any]:
        """异步获取应用健康状态"""
        try:
            # 模拟异步检查
            await asyncio.sleep(0.01)

            uptime_seconds = time.time() - self._start_time
            uptime_str = self._format_uptime(uptime_seconds)

            return {
                "status": HealthStatus.UP.value,
                "details": {"name": "Mini-Boot Application", "uptime": uptime_str, "uptime_seconds": round(uptime_seconds, 2), "version": "1.0.0"},
                "timestamp": datetime.now().isoformat(),
            }
        except Exception as e:
            logger.error(f"Application health check failed: {e}")
            return {"status": HealthStatus.DOWN.value, "details": {"error": str(e)}, "timestamp": datetime.now().isoformat()}

    def _format_uptime(self, seconds: float) -> str:
        """格式化运行时间"""
        if seconds < 60:
            return f"{seconds:.1f} seconds"
        elif seconds < 3600:
            return f"{seconds / 60:.1f} minutes"
        elif seconds < 86400:
            return f"{seconds / 3600:.1f} hours"
        else:
            return f"{seconds / 86400:.1f} days"


class AsyncCpuHealthIndicator(AsyncHealthIndicator):
    """异步 CPU 健康指标"""

    def __init__(self, threshold_percent: float = 80.0, timeout: float = 2.0):
        """初始化异步 CPU 健康指标

        Args:
            threshold_percent: CPU 使用率阈值(百分比)
            timeout: 检查超时时间(秒)
        """
        super().__init__("cpu", timeout)
        self.threshold_percent = threshold_percent

    @timeout(2.0)
    async def health_async(self) -> Dict[str, Any]:
        """异步获取 CPU 健康状态"""
        try:
            # 在线程池中执行同步 I/O 操作
            loop = asyncio.get_event_loop()
            cpu_percent = await loop.run_in_executor(None, psutil.cpu_percent, 1.0)
            cpu_count = await loop.run_in_executor(None, psutil.cpu_count)

            status = HealthStatus.UP if cpu_percent < self.threshold_percent else HealthStatus.DOWN

            return {
                "status": status.value,
                "details": {
                    "usage": f"{cpu_percent:.1f}%",
                    "cores": cpu_count,
                    "threshold": f"{self.threshold_percent}%",
                    "load_average": await self._get_load_average(),
                },
                "timestamp": datetime.now().isoformat(),
            }
        except Exception as e:
            logger.error(f"CPU health check failed: {e}")
            return {"status": HealthStatus.DOWN.value, "details": {"error": str(e)}, "timestamp": datetime.now().isoformat()}

    async def _get_load_average(self) -> Dict[str, float]:
        """获取系统负载平均值"""
        try:
            loop = asyncio.get_event_loop()
            load_avg = await loop.run_in_executor(None, psutil.getloadavg)
            return {"1min": round(load_avg[0], 2), "5min": round(load_avg[1], 2), "15min": round(load_avg[2], 2)}
        except (AttributeError, OSError):
            # Windows 系统不支持 getloadavg
            return {"1min": 0.0, "5min": 0.0, "15min": 0.0}






class AsyncHealthEndpoint(EndpointProvider):
    """异步健康检查端点

    提供高性能的异步健康检查,支持并发执行多个健康指标检查.
    """

    def __init__(
        self,
        cache_ttl: float = 30.0,  # 30秒缓存
        max_concurrent: int = 20,
        timeout: float = 10.0,
    ):
        """初始化异步健康检查端点

        Args:
            cache_ttl: 缓存TTL(秒)
            max_concurrent: 最大并发数
            timeout: 总超时时间(秒)
        """
        # 初始化基本属性(替代 super().__init__)
        self.endpoint_id = "health"
        self.enabled = True
        self.sensitive = False
        self.cache_enabled = True
        self.cache_ttl = cache_ttl
        self.max_concurrent = max_concurrent
        self.timeout = timeout

        # 注册默认健康指标
        self.indicators: Dict[str, AsyncHealthIndicator] = {
            "disk": AsyncDiskSpaceHealthIndicator(),
            "memory": AsyncMemoryHealthIndicator(),
            "application": AsyncApplicationHealthIndicator(),
            "cpu": AsyncCpuHealthIndicator(),
        }

        logger.info(f"AsyncHealthEndpoint initialized with {len(self.indicators)} indicators")

    async def _execute_operation_async(self, operation_type: OperationType, **kwargs) -> Any:
        """执行异步操作"""
        if operation_type == OperationType.READ:
            return await self._get_health_async(**kwargs)
        else:
            raise ValueError(f"Unsupported operation: {operation_type}")

    def _create_operations(self) -> List[EndpointOperation]:
        """创建操作列表"""
        return [EndpointOperation(operation_type=OperationType.READ, method="GET", handler=self._get_health_async)]

    async def _execute_operation_async(self, operation_type: OperationType, **kwargs) -> Any:
        """执行异步操作"""
        if operation_type == OperationType.READ:
            return await self._get_health_async(**kwargs)
        else:
            raise ValueError(f"Unsupported operation: {operation_type}")

    @cached(ttl=30.0)
    async def _get_health_async(self, **kwargs) -> Dict[str, Any]:
        """异步获取健康状态 - 并发执行所有健康检查"""
        start_time = time.time()

        try:
            # 并发执行所有健康指标检查
            health_tasks = []
            for name, indicator in self.indicators.items():
                task = asyncio.create_task(self._check_indicator_with_timeout(name, indicator), name=f"health_check_{name}")
                health_tasks.append(task)

            # 等待所有健康检查完成
            health_results = await asyncio.gather(*health_tasks, return_exceptions=True)

            # 处理结果
            details = {}
            overall_status = HealthStatus.UP

            for i, (name, result) in enumerate(zip(self.indicators.keys(), health_results)):
                if isinstance(result, Exception):
                    # 处理异常
                    details[name] = {"status": HealthStatus.DOWN.value, "details": {"error": str(result)}, "timestamp": datetime.now().isoformat()}
                    overall_status = HealthStatus.DOWN
                    logger.error(f"Health check failed for {name}: {result}")
                else:
                    details[name] = result
                    if result.get("status") == HealthStatus.DOWN.value:
                        overall_status = HealthStatus.DOWN

            execution_time = time.time() - start_time

            return {
                "status": overall_status.value,
                "timestamp": datetime.now().isoformat(),
                "details": details,
                "performance": {
                    "execution_time_ms": round(execution_time * 1000, 2),
                    "indicators_count": len(self.indicators),
                    "concurrent_execution": True,
                },
            }

        except Exception as e:
            logger.error(f"Health check execution failed: {e}")
            return {
                "status": HealthStatus.DOWN.value,
                "timestamp": datetime.now().isoformat(),
                "details": {"error": str(e)},
                "performance": {
                    "execution_time_ms": round((time.time() - start_time) * 1000, 2),
                    "indicators_count": len(self.indicators),
                    "concurrent_execution": False,
                },
            }

    async def _check_indicator_with_timeout(self, name: str, indicator: AsyncHealthIndicator) -> Dict[str, Any]:
        """带超时的健康指标检查"""
        try:
            return await asyncio.wait_for(indicator.health_async(), timeout=indicator.timeout)
        except asyncio.TimeoutError:
            logger.warning(f"Health check timeout for {name} after {indicator.timeout}s")
            return {
                "status": HealthStatus.DOWN.value,
                "details": {"error": f"Timeout after {indicator.timeout}s"},
                "timestamp": datetime.now().isoformat(),
            }
        except Exception as e:
            logger.error(f"Health check error for {name}: {e}")
            return {"status": HealthStatus.DOWN.value, "details": {"error": str(e)}, "timestamp": datetime.now().isoformat()}

    def register_indicator(self, name: str, indicator: AsyncHealthIndicator) -> None:
        """注册异步健康指标"""
        if not isinstance(indicator, AsyncHealthIndicator):
            raise TypeError("indicator must be an instance of AsyncHealthIndicator")
        self.indicators[name] = indicator
        logger.info(f"Registered async health indicator: {name}")

    def unregister_indicator(self, name: str) -> bool:
        """注销健康指标"""
        if name in self.indicators:
            del self.indicators[name]
            logger.info(f"Unregistered health indicator: {name}")
            return True
        return False

    def get_indicator(self, name: str) -> Optional[AsyncHealthIndicator]:
        """获取健康指标"""
        return self.indicators.get(name)

    def get_all_indicators(self) -> Dict[str, AsyncHealthIndicator]:
        """获取所有健康指标"""
        return self.indicators.copy()

    def __str__(self) -> str:
        return f"AsyncHealthEndpoint(indicators={len(self.indicators)}, cache_enabled={self.cache_enabled})"

    # EndpointProvider 接口实现
    def get_endpoint_info(self) -> EndpointInfo:
        """获取端点信息 - EndpointProvider 接口实现"""
        return EndpointInfo(
            name=self.endpoint_id,
            path="/actuator/health",
            methods=["GET"],
            description="Health check endpoint providing application health status",
            enabled=self.enabled,
            sensitive=self.sensitive
        )

    async def handle_request(self, **kwargs) -> Any:
        """处理请求 - EndpointProvider 接口实现"""
        detailed = kwargs.get('detailed', False)
        return await self.health(detailed=detailed)

    def is_enabled(self) -> bool:
        """检查端点是否启用 - EndpointProvider 接口实现"""
        return self.enabled
