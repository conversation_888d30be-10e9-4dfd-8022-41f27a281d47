#!/usr/bin/env python
# encoding: utf-8
"""
测试数据收集器模块迁移功能

验证从 miniboot/actuator/collectors/ 迁移到 miniboot/starters/actuator/collectors/ 后的功能完整性
"""

import unittest
from unittest.mock import Mock, patch

from miniboot.starters.actuator import (AppMetricsCollector,
                                        AsyncCustomHealthIndicator,
                                        AsyncDatabaseHealthIndicator,
                                        AsyncDiskSpaceHealthIndicator,
                                        AsyncHealthCollector,
                                        AsyncHealthIndicator,
                                        AsyncMemoryHealthIndicator,
                                        HealthStatus, MetricsAggregator,
                                        SystemMetricsCollector)


class TestCollectorsMigration(unittest.TestCase):
    """测试数据收集器模块迁移"""

    def test_system_metrics_collector_basic(self):
        """测试系统指标收集器基本功能"""
        collector = SystemMetricsCollector()

        # 验证收集器基本属性
        self.assertIsInstance(collector, SystemMetricsCollector)
        self.assertTrue(hasattr(collector, 'collect_all'))
        self.assertTrue(hasattr(collector, 'collect_specific'))

    def test_app_metrics_collector_basic(self):
        """测试应用指标收集器基本功能"""
        collector = AppMetricsCollector()

        # 验证收集器基本属性
        self.assertIsInstance(collector, AppMetricsCollector)
        self.assertTrue(hasattr(collector, 'collect_all'))
        self.assertTrue(hasattr(collector, 'collect_specific'))

    def test_metrics_aggregator_basic(self):
        """测试指标聚合器基本功能"""
        aggregator = MetricsAggregator()

        # 验证聚合器基本属性
        self.assertIsInstance(aggregator, MetricsAggregator)
        self.assertTrue(hasattr(aggregator, 'collect_all'))
        self.assertTrue(hasattr(aggregator, 'collect_specific'))

    def test_async_health_collector_basic(self):
        """测试异步健康收集器基本功能"""
        collector = AsyncHealthCollector()

        # 验证收集器基本属性
        self.assertIsInstance(collector, AsyncHealthCollector)
        self.assertTrue(hasattr(collector, 'collect_health'))
        self.assertTrue(hasattr(collector, 'register_indicator'))

    def test_health_status_enum(self):
        """测试健康状态枚举"""
        # 验证健康状态枚举值
        self.assertEqual(HealthStatus.UP.value, "UP")
        self.assertEqual(HealthStatus.DOWN.value, "DOWN")
        self.assertEqual(HealthStatus.UNKNOWN.value, "UNKNOWN")

    def test_health_indicators_available(self):
        """测试健康指示器可用性"""
        # 验证健康指示器类可以正常导入和使用
        self.assertTrue(hasattr(AsyncHealthIndicator, '__init__'))
        self.assertTrue(hasattr(AsyncDiskSpaceHealthIndicator, '__init__'))
        self.assertTrue(hasattr(AsyncMemoryHealthIndicator, '__init__'))
        self.assertTrue(hasattr(AsyncDatabaseHealthIndicator, '__init__'))
        self.assertTrue(hasattr(AsyncCustomHealthIndicator, '__init__'))

    def test_health_indicator_creation(self):
        """测试健康指示器创建"""
        # 测试磁盘空间健康指示器
        disk_indicator = AsyncDiskSpaceHealthIndicator()
        self.assertIsInstance(disk_indicator, AsyncDiskSpaceHealthIndicator)

        # 测试内存健康指示器
        memory_indicator = AsyncMemoryHealthIndicator()
        self.assertIsInstance(memory_indicator, AsyncMemoryHealthIndicator)

    def test_all_collector_classes_importable(self):
        """测试所有收集器类都可以正常导入"""
        from miniboot.starters.actuator.collectors import (
            AppMetricsCollector, AsyncHealthCollector, MetricsAggregator,
            SystemMetricsCollector)

        # 验证所有类都可以实例化
        collectors = [
            SystemMetricsCollector(),
            AppMetricsCollector(),
            MetricsAggregator(),
            AsyncHealthCollector()
        ]

        for collector in collectors:
            self.assertTrue(hasattr(collector, '__class__'))
            self.assertIsNotNone(collector)

    def test_collector_module_structure(self):
        """测试收集器模块结构完整性"""
        import miniboot.starters.actuator.collectors as collectors_module

        # 验证主要收集器类存在
        required_classes = [
            'SystemMetricsCollector', 'AppMetricsCollector',
            'MetricsAggregator', 'AsyncHealthCollector'
        ]

        for class_name in required_classes:
            self.assertTrue(hasattr(collectors_module, class_name),
                          f"收集器模块缺少 {class_name} 类")

    def test_migration_import_paths(self):
        """测试迁移后的导入路径正确性"""
        # 测试从新位置导入
        try:
            from miniboot.starters.actuator.collectors.aggregator import \
                MetricsAggregator
            from miniboot.starters.actuator.collectors.health import \
                AsyncHealthCollector
            from miniboot.starters.actuator.collectors.metrics import \
                AppMetricsCollector
            from miniboot.starters.actuator.collectors.system import \
                SystemMetricsCollector

            # 验证导入成功
            self.assertTrue(True, "所有收集器类导入成功")

        except ImportError as e:
            self.fail(f"收集器类导入失败: {e}")

    def test_collector_functionality_interfaces(self):
        """测试收集器功能接口"""
        # 系统指标收集器
        system_collector = SystemMetricsCollector()
        self.assertTrue(hasattr(system_collector, 'collect_all'))
        self.assertTrue(hasattr(system_collector, 'collect_specific'))
        self.assertTrue(hasattr(system_collector, 'get_supported_metrics'))

        # 应用指标收集器
        app_collector = AppMetricsCollector()
        self.assertTrue(hasattr(app_collector, 'collect_all'))
        self.assertTrue(hasattr(app_collector, 'collect_specific'))

        # 指标聚合器
        aggregator = MetricsAggregator()
        self.assertTrue(hasattr(aggregator, 'collect_all'))
        self.assertTrue(hasattr(aggregator, 'collect_specific'))
        self.assertTrue(hasattr(aggregator, 'get_supported_collectors'))

    def test_health_collector_indicators(self):
        """测试健康收集器指示器管理"""
        collector = AsyncHealthCollector()

        # 验证指示器管理方法
        self.assertTrue(hasattr(collector, 'register_indicator'))
        self.assertTrue(hasattr(collector, 'unregister_indicator'))
        self.assertTrue(hasattr(collector, 'get_indicator_names'))
        self.assertTrue(hasattr(collector, 'collect_health'))

    def test_main_module_exports(self):
        """测试主模块导出完整性"""
        # 测试从主模块导入所有收集器
        try:
            from miniboot.starters.actuator import (AppMetricsCollector,
                                                    AsyncHealthCollector,
                                                    AsyncHealthIndicator,
                                                    HealthStatus,
                                                    MetricsAggregator,
                                                    SystemMetricsCollector)

            # 验证所有类都可以正常使用
            system = SystemMetricsCollector()
            app = AppMetricsCollector()
            aggregator = MetricsAggregator()
            health = AsyncHealthCollector()

            self.assertIsNotNone(system)
            self.assertIsNotNone(app)
            self.assertIsNotNone(aggregator)
            self.assertIsNotNone(health)
            self.assertEqual(HealthStatus.UP.value, "UP")

        except Exception as e:
            self.fail(f"主模块导出测试失败: {e}")


if __name__ == '__main__':
    unittest.main()
