#!/usr/bin/env python3
"""
测试 Actuator 自动配置加载机制
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from miniboot.context.application import DefaultApplicationContext
from miniboot.env.environment import StandardEnvironment


async def test_actuator_auto_config_loading():
    """测试 Actuator 自动配置加载"""
    print("🔍 Testing Actuator Auto Configuration Loading...")

    try:
        # 创建环境 - 修复 profiles 设置
        environment = StandardEnvironment()
        environment.set_active_profiles('dev')  # 传递字符串而不是列表

        # 创建应用上下文
        context = DefaultApplicationContext()
        context._environment = environment

        # 手动调用自动配置加载方法 - 使用 await
        print("  🔄 Calling _execute_auto_configurations...")
        await context._execute_auto_configurations()

        print("  ✅ Auto configuration loading completed")

        # 检查 Bean 工厂中是否有 Actuator 相关的 Bean
        bean_names = context.get_bean_factory().names()
        actuator_beans = [name for name in bean_names if 'actuator' in name.lower()]

        print(f"  📊 Total beans in factory: {len(bean_names)}")
        print(f"  📊 Actuator-related beans: {len(actuator_beans)}")

        if actuator_beans:
            for bean_name in actuator_beans:
                print(f"    📦 {bean_name}")

        # 测试监控组件发现
        from miniboot.monitoring.interfaces import (EndpointProvider,
                                                    MonitoringContext)

        # 检查 Bean 工厂中的类型查找
        monitoring_contexts_beans = context.get_beans_by_type(MonitoringContext)
        endpoint_providers_beans = context.get_beans_by_type(EndpointProvider)

        print(f"  📊 MonitoringContext beans in factory: {len(monitoring_contexts_beans)}")
        print(f"  📊 EndpointProvider beans in factory: {len(endpoint_providers_beans)}")

        if monitoring_contexts_beans:
            for name, bean in monitoring_contexts_beans.items():
                print(f"    📦 MonitoringContext: {name} -> {type(bean).__name__}")

        if endpoint_providers_beans:
            for name, bean in endpoint_providers_beans.items():
                print(f"    📦 EndpointProvider: {name} -> {type(bean).__name__}")

        # 判断成功条件
        success = (
            len(actuator_beans) > 0 or
            len(monitoring_contexts_beans) > 0 or
            len(endpoint_providers_beans) > 0
        )

        print(f"  🎯 Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
        return success

    except Exception as e:
        print(f"  ❌ Error in auto configuration loading: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🎯 Testing Actuator Auto Configuration Loading")
    print("=" * 60)

    success = await test_actuator_auto_config_loading()

    print(f"\n🎯 Overall Result: {'✅ SUCCESS' if success else '❌ FAILED'}")

    if not success:
        print("\n🔧 Issue: Actuator auto-configuration is not being loaded during framework startup")
        print("  This explains why monitoring component discovery finds 0 implementations")

if __name__ == "__main__":
    asyncio.run(main())
