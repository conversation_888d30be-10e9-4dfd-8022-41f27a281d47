#!/usr/bin/env python
# encoding: utf-8
"""
测试端点模块迁移功能

验证从 miniboot/actuator/endpoints/ 迁移到 miniboot/starters/actuator/endpoints/ 后的功能完整性
"""

import unittest
from unittest.mock import Mock, patch

from miniboot.starters.actuator import (BeansEndpoint, Counter, CustomEndpoint,
                                        Gauge, HealthEndpoint, HealthIndicator,
                                        HealthStatus, InfoContributor,
                                        InfoEndpoint, MetricsEndpoint,
                                        MetricsRegistry, Timer,
                                        create_beans_endpoint)


class TestEndpointsMigration(unittest.TestCase):
    """测试端点模块迁移"""

    def test_health_endpoint_basic(self):
        """测试健康检查端点基本功能"""
        endpoint = HealthEndpoint()

        # 验证端点基本属性
        self.assertEqual(endpoint.id, "health")
        self.assertTrue(endpoint.enabled)
        self.assertFalse(endpoint.sensitive)

        # 验证健康状态枚举
        self.assertEqual(HealthStatus.UP.value, "UP")
        self.assertEqual(HealthStatus.DOWN.value, "DOWN")

    def test_info_endpoint_basic(self):
        """测试信息端点基本功能"""
        endpoint = InfoEndpoint()

        # 验证端点基本属性
        self.assertEqual(endpoint.id, "info")
        self.assertTrue(endpoint.enabled)
        self.assertFalse(endpoint.sensitive)

    def test_metrics_endpoint_basic(self):
        """测试指标端点基本功能"""
        endpoint = MetricsEndpoint()

        # 验证端点基本属性
        self.assertEqual(endpoint.id, "metrics")
        self.assertTrue(endpoint.enabled)
        self.assertFalse(endpoint.sensitive)

    def test_beans_endpoint_basic(self):
        """测试Bean端点基本功能"""
        endpoint = BeansEndpoint()

        # 验证端点基本属性
        self.assertEqual(endpoint.id, "beans")
        self.assertTrue(endpoint.enabled)
        self.assertFalse(endpoint.sensitive)

    def test_custom_endpoint_basic(self):
        """测试自定义端点基本功能"""
        endpoint = CustomEndpoint(endpoint_id="test", description="Test endpoint")

        # 验证端点基本属性
        self.assertEqual(endpoint.id, "test")
        self.assertTrue(endpoint.enabled)
        self.assertFalse(endpoint.sensitive)

    def test_metrics_types_available(self):
        """测试指标类型可用性"""
        # 验证指标类型可以正常导入和使用
        self.assertTrue(hasattr(Counter, '__init__'))
        self.assertTrue(hasattr(Gauge, '__init__'))
        self.assertTrue(hasattr(Timer, '__init__'))
        self.assertTrue(hasattr(MetricsRegistry, '__init__'))

    def test_health_indicator_interface(self):
        """测试健康指示器接口"""
        # 验证健康指示器类可以正常导入
        self.assertTrue(hasattr(HealthIndicator, 'check_health'))

    def test_info_contributor_interface(self):
        """测试信息贡献者接口"""
        # 验证信息贡献者类可以正常导入
        self.assertTrue(hasattr(InfoContributor, 'contribute'))

    def test_create_beans_endpoint_function(self):
        """测试Bean端点创建函数"""
        # 模拟应用上下文
        mock_context = Mock()
        mock_context.get_bean_factory.return_value = Mock()

        # 测试创建函数
        endpoint = create_beans_endpoint(mock_context)
        self.assertIsInstance(endpoint, BeansEndpoint)
        self.assertEqual(endpoint.id, "beans")

    def test_all_endpoint_classes_importable(self):
        """测试所有端点类都可以正常导入"""
        from miniboot.starters.actuator.endpoints import (BeansEndpoint,
                                                          CustomEndpoint,
                                                          HealthEndpoint,
                                                          InfoEndpoint,
                                                          MetricsEndpoint)

        # 验证所有类都可以实例化
        endpoints = [
            HealthEndpoint(),
            InfoEndpoint(),
            MetricsEndpoint(),
            BeansEndpoint(),
            CustomEndpoint("test", description="Test endpoint")
        ]

        for endpoint in endpoints:
            self.assertTrue(hasattr(endpoint, 'id'))
            self.assertTrue(hasattr(endpoint, 'enabled'))
            self.assertTrue(hasattr(endpoint, 'sensitive'))

    def test_endpoint_module_structure(self):
        """测试端点模块结构完整性"""
        import miniboot.starters.actuator.endpoints as endpoints_module

        # 验证主要端点类存在
        required_classes = [
            'BeansEndpoint', 'HealthEndpoint', 'InfoEndpoint',
            'MetricsEndpoint', 'CustomEndpoint'
        ]

        for class_name in required_classes:
            self.assertTrue(hasattr(endpoints_module, class_name),
                          f"端点模块缺少 {class_name} 类")

    def test_migration_import_paths(self):
        """测试迁移后的导入路径正确性"""
        # 测试从新位置导入
        try:
            from miniboot.starters.actuator.endpoints.beans import \
                BeansEndpoint
            from miniboot.starters.actuator.endpoints.custom import \
                CustomEndpoint
            from miniboot.starters.actuator.endpoints.health import \
                HealthEndpoint
            from miniboot.starters.actuator.endpoints.info import InfoEndpoint
            from miniboot.starters.actuator.endpoints.metrics import \
                MetricsEndpoint

            # 验证导入成功
            self.assertTrue(True, "所有端点类导入成功")

        except ImportError as e:
            self.fail(f"端点类导入失败: {e}")


if __name__ == '__main__':
    unittest.main()
