#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Web 集成检查机制

实现 Actuator 与 Web 框架集成的条件检查功能，包括：
- FastAPI 可用性检查
- Web 模块启用状态检查
- 综合集成条件判断
- 配置属性验证

设计原则：
- 条件化集成：只有在满足所有条件时才启用 Web 集成
- 优雅降级：当 Web 框架不可用时，Actuator 仍可独立运行
- 配置驱动：通过配置属性控制集成行为
- 环境感知：自动检测运行环境和可用组件

使用示例：
    # 基本使用
    checker = WebIntegrationChecker()
    if checker.should_enable_web_integration():
        print("Web 集成已启用")
    
    # 带配置的使用
    from miniboot.starters.actuator.properties import ActuatorStarterProperties
    properties = ActuatorStarterProperties()
    checker = WebIntegrationChecker(properties)
    
    # 详细检查
    status = checker.get_integration_status()
    print(f"FastAPI 可用: {status['fastapi_available']}")
    print(f"Web 模块启用: {status['web_module_enabled']}")
"""

import importlib
import sys
from dataclasses import dataclass
from typing import Any, Dict, Optional

from loguru import logger


@dataclass
class IntegrationStatus:
    """Web 集成状态信息"""
    
    # 基础检查结果
    fastapi_available: bool = False
    fastapi_version: Optional[str] = None
    web_module_enabled: bool = False
    actuator_web_enabled: bool = True
    
    # 配置检查结果
    starters_web_enabled: bool = False
    web_properties_valid: bool = True
    base_path_configured: bool = True
    
    # 综合判断结果
    should_integrate: bool = False
    integration_reason: str = ""
    
    # 错误信息
    errors: list = None
    warnings: list = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


class WebIntegrationChecker:
    """Web 集成检查器
    
    负责检查 Actuator 与 Web 框架集成的所有必要条件，
    包括框架可用性、配置状态、环境兼容性等。
    
    检查流程：
    1. 检查 FastAPI 框架可用性
    2. 检查 Web 模块启用状态
    3. 验证相关配置属性
    4. 综合判断是否应该启用集成
    """
    
    def __init__(self, properties: Optional[Any] = None):
        """初始化 Web 集成检查器
        
        Args:
            properties: Actuator 配置属性实例，可选
        """
        self._properties = properties
        self._cached_status: Optional[IntegrationStatus] = None
        self._cache_valid = False
        
        logger.debug("WebIntegrationChecker initialized")
    
    def is_fastapi_available(self) -> bool:
        """检查 FastAPI 是否可用
        
        尝试导入 FastAPI 模块，检查其可用性和版本兼容性。
        
        Returns:
            bool: FastAPI 是否可用
        """
        try:
            import fastapi
            
            # 检查版本兼容性（可选）
            version = getattr(fastapi, '__version__', 'unknown')
            logger.debug(f"FastAPI available, version: {version}")
            
            # 检查关键组件是否可用
            from fastapi import FastAPI
            from fastapi.routing import APIRouter
            
            logger.debug("FastAPI core components verified")
            return True
            
        except ImportError as e:
            logger.debug(f"FastAPI not available: {e}")
            return False
        except Exception as e:
            logger.warning(f"FastAPI availability check failed: {e}")
            return False
    
    def get_fastapi_version(self) -> Optional[str]:
        """获取 FastAPI 版本信息
        
        Returns:
            Optional[str]: FastAPI 版本号，如果不可用则返回 None
        """
        try:
            import fastapi
            return getattr(fastapi, '__version__', 'unknown')
        except ImportError:
            return None
    
    def is_web_module_enabled(self) -> bool:
        """检查 Web 模块是否启用
        
        检查配置中的 Web 模块启用状态，包括：
        - starters.web.enabled
        - starters.actuator.web.enabled
        
        Returns:
            bool: Web 模块是否启用
        """
        try:
            # 如果没有提供配置属性，尝试从环境中获取
            if self._properties is None:
                return self._check_web_enabled_from_environment()
            
            # 从配置属性中检查
            return self._check_web_enabled_from_properties()
            
        except Exception as e:
            logger.debug(f"Web module enabled check failed: {e}")
            # 默认启用，除非明确禁用
            return True
    
    def _check_web_enabled_from_environment(self) -> bool:
        """从环境变量或系统属性检查 Web 启用状态"""
        import os
        
        # 检查环境变量
        starters_web_enabled = os.getenv('STARTERS_WEB_ENABLED', 'false').lower() == 'true'
        actuator_web_enabled = os.getenv('STARTERS_ACTUATOR_WEB_ENABLED', 'true').lower() == 'true'
        
        logger.debug(f"Environment check - starters.web.enabled: {starters_web_enabled}, "
                    f"starters.actuator.web.enabled: {actuator_web_enabled}")
        
        # starters.web.enabled 必须为 true，starters.actuator.web.enabled 默认为 true
        return starters_web_enabled and actuator_web_enabled
    
    def _check_web_enabled_from_properties(self) -> bool:
        """从配置属性检查 Web 启用状态"""
        try:
            # 检查 starters.web.enabled（必须明确启用）
            starters_web_enabled = False
            if hasattr(self._properties, 'web') and hasattr(self._properties.web, 'enabled'):
                starters_web_enabled = self._properties.web.enabled
            
            # 检查 starters.actuator.web.enabled（默认启用）
            actuator_web_enabled = True
            if hasattr(self._properties, 'web') and hasattr(self._properties.web, 'enabled'):
                actuator_web_enabled = getattr(self._properties.web, 'enabled', True)
            
            logger.debug(f"Properties check - starters.web.enabled: {starters_web_enabled}, "
                        f"starters.actuator.web.enabled: {actuator_web_enabled}")
            
            return starters_web_enabled and actuator_web_enabled
            
        except Exception as e:
            logger.debug(f"Properties check failed: {e}")
            return True  # 默认启用
    
    def should_enable_web_integration(self) -> bool:
        """综合判断是否应该启用 Web 集成
        
        综合考虑所有检查条件，决定是否启用 Web 集成：
        1. FastAPI 必须可用
        2. Web 模块必须启用
        3. 配置属性必须有效
        
        Returns:
            bool: 是否应该启用 Web 集成
        """
        status = self.get_integration_status()
        return status.should_integrate
    
    def get_integration_status(self) -> IntegrationStatus:
        """获取完整的集成状态信息
        
        执行所有检查并返回详细的状态信息，包括：
        - 各项检查的结果
        - 错误和警告信息
        - 最终的集成决策
        
        Returns:
            IntegrationStatus: 完整的集成状态信息
        """
        # 使用缓存避免重复检查
        if self._cache_valid and self._cached_status is not None:
            return self._cached_status
        
        status = IntegrationStatus()
        
        # 1. 检查 FastAPI 可用性
        status.fastapi_available = self.is_fastapi_available()
        if status.fastapi_available:
            status.fastapi_version = self.get_fastapi_version()
        else:
            status.errors.append("FastAPI framework is not available")
        
        # 2. 检查 Web 模块启用状态
        status.web_module_enabled = self.is_web_module_enabled()
        if not status.web_module_enabled:
            status.errors.append("Web module is not enabled in configuration")
        
        # 3. 检查 starters.web.enabled 状态
        status.starters_web_enabled = self._check_starters_web_enabled()
        if not status.starters_web_enabled:
            status.errors.append("starters.web.enabled is not set to true")
        
        # 4. 验证配置属性
        status.web_properties_valid = self._validate_web_properties()
        if not status.web_properties_valid:
            status.warnings.append("Some web properties are invalid or missing")
        
        # 5. 检查基础路径配置
        status.base_path_configured = self._check_base_path_configuration()
        if not status.base_path_configured:
            status.warnings.append("Base path configuration may be invalid")
        
        # 6. 综合判断
        status.should_integrate = (
            status.fastapi_available and 
            status.web_module_enabled and 
            status.starters_web_enabled
        )
        
        # 7. 设置集成原因
        if status.should_integrate:
            status.integration_reason = "All conditions met for Web integration"
        else:
            reasons = []
            if not status.fastapi_available:
                reasons.append("FastAPI not available")
            if not status.web_module_enabled:
                reasons.append("Web module disabled")
            if not status.starters_web_enabled:
                reasons.append("starters.web.enabled is false")
            status.integration_reason = f"Integration disabled: {', '.join(reasons)}"
        
        # 缓存结果
        self._cached_status = status
        self._cache_valid = True
        
        logger.debug(f"Web integration status: {status.should_integrate}, reason: {status.integration_reason}")
        return status
    
    def _check_starters_web_enabled(self) -> bool:
        """检查 starters.web.enabled 配置"""
        try:
            if self._properties is None:
                # 从环境变量检查
                import os
                return os.getenv('STARTERS_WEB_ENABLED', 'false').lower() == 'true'
            
            # 从配置属性检查
            if hasattr(self._properties, 'web') and hasattr(self._properties.web, 'enabled'):
                return self._properties.web.enabled
            
            return False  # 默认禁用，需要明确启用
            
        except Exception as e:
            logger.debug(f"starters.web.enabled check failed: {e}")
            return False
    
    def _validate_web_properties(self) -> bool:
        """验证 Web 相关配置属性"""
        try:
            if self._properties is None:
                return True  # 没有配置时认为有效
            
            # 检查基本的 Web 配置
            if hasattr(self._properties, 'web'):
                web_config = self._properties.web
                
                # 检查端口配置
                if hasattr(web_config, 'port') and web_config.port is not None:
                    if not isinstance(web_config.port, int) or web_config.port < 1 or web_config.port > 65535:
                        return False
                
                # 检查基础路径配置
                if hasattr(web_config, 'base_path') and web_config.base_path is not None:
                    if not isinstance(web_config.base_path, str) or not web_config.base_path.startswith('/'):
                        return False
            
            return True
            
        except Exception as e:
            logger.debug(f"Web properties validation failed: {e}")
            return False
    
    def _check_base_path_configuration(self) -> bool:
        """检查基础路径配置"""
        try:
            if self._properties is None:
                return True  # 没有配置时使用默认值
            
            if hasattr(self._properties, 'web') and hasattr(self._properties.web, 'base_path'):
                base_path = self._properties.web.base_path
                if base_path is not None:
                    return isinstance(base_path, str) and base_path.startswith('/')
            
            return True  # 默认配置有效
            
        except Exception as e:
            logger.debug(f"Base path configuration check failed: {e}")
            return True
    
    def invalidate_cache(self) -> None:
        """使缓存失效，强制重新检查"""
        self._cache_valid = False
        self._cached_status = None
        logger.debug("WebIntegrationChecker cache invalidated")
    
    def get_integration_summary(self) -> Dict[str, Any]:
        """获取集成状态摘要
        
        Returns:
            Dict[str, Any]: 集成状态摘要信息
        """
        status = self.get_integration_status()
        
        return {
            'enabled': status.should_integrate,
            'reason': status.integration_reason,
            'fastapi_available': status.fastapi_available,
            'fastapi_version': status.fastapi_version,
            'web_module_enabled': status.web_module_enabled,
            'starters_web_enabled': status.starters_web_enabled,
            'errors': status.errors,
            'warnings': status.warnings,
            'timestamp': __import__('time').time()
        }
